import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Avatar,
  LinearProgress,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Chip
} from '@mui/material';
import {
  People,
  VideoLibrary,
  Category,
  WorkspacePremium,
  TrendingUp,
  School,
  CheckCircle,
  AccessTime
} from '@mui/icons-material';

const StatCard = ({ title, value, icon, color, subtitle, progress }) => (
  <Card sx={{ height: '100%', position: 'relative', overflow: 'visible' }}>
    <CardContent>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box>
          <Typography color="textSecondary" gutterBottom variant="h6">
            {title}
          </Typography>
          <Typography variant="h4" sx={{ fontWeight: 700, color: color }}>
            {value}
          </Typography>
          {subtitle && (
            <Typography variant="body2" color="textSecondary">
              {subtitle}
            </Typography>
          )}
        </Box>
        <Avatar
          sx={{
            bgcolor: color,
            width: 56,
            height: 56,
            boxShadow: `0 8px 16px ${color}40`
          }}
        >
          {icon}
        </Avatar>
      </Box>
      {progress !== undefined && (
        <Box sx={{ mt: 2 }}>
          <LinearProgress
            variant="determinate"
            value={progress}
            sx={{
              height: 8,
              borderRadius: 4,
              bgcolor: `${color}20`,
              '& .MuiLinearProgress-bar': {
                bgcolor: color,
                borderRadius: 4
              }
            }}
          />
          <Typography variant="caption" color="textSecondary" sx={{ mt: 0.5 }}>
            {progress}% من الهدف
          </Typography>
        </Box>
      )}
    </CardContent>
  </Card>
);

const DashboardOverview = () => {
  const [stats, setStats] = useState({
    totalStudents: 0,
    totalCourses: 0,
    totalVideos: 0,
    totalCertificates: 0
  });
  const [courses, setCourses] = useState([]);
  const [students, setStudents] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);

      // جلب إحصائيات لوحة التحكم
      const statsResponse = await axios.get('/admin/dashboard-stats');
      setStats(statsResponse.data);

      // جلب الكورسات
      const coursesResponse = await axios.get('/admin/courses');
      setCourses(coursesResponse.data);

      // جلب الطلاب
      const studentsResponse = await axios.get('/admin/students');
      setStudents(studentsResponse.data);

    } catch (error) {
      console.error('خطأ في جلب بيانات لوحة التحكم:', error);
      // بيانات احتياطية
      setStats({
        totalStudents: 2,
        totalCourses: 3,
        totalVideos: 15,
        totalCertificates: 1
      });
    } finally {
      setLoading(false);
    }
  };
  const recentActivities = [
    {
      id: 1,
      type: 'student',
      title: 'طالب جديد انضم',
      description: 'أحمد محمد انضم إلى المنصة',
      time: 'منذ 5 دقائق',
      icon: <People />,
      color: '#2196F3'
    },
    {
      id: 2,
      type: 'course',
      title: 'كورس جديد تم إضافته',
      description: 'أساسيات التسويق الرقمي',
      time: 'منذ ساعة',
      icon: <VideoLibrary />,
      color: '#4CAF50'
    },
    {
      id: 3,
      type: 'certificate',
      title: 'شهادة جديدة تم إصدارها',
      description: 'سارة أحمد أكملت كورس التسويق',
      time: 'منذ ساعتين',
      icon: <WorkspacePremium />,
      color: '#FF9800'
    }
  ];

  return (
    <Box>
      {/* Welcome Section */}
      <Paper
        sx={{
          p: 4,
          mb: 4,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          borderRadius: 3
        }}
      >
        <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
          مرحباً بك في لوحة التحكم
        </Typography>
        <Typography variant="h6" sx={{ opacity: 0.9 }}>
          إدارة منصة كورسات علاء عبد الحميد
        </Typography>
      </Paper>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="إجمالي الطلاب"
            value={stats.totalStudents}
            icon={<People />}
            color="#2196F3"
            subtitle={`${students.filter(s => s.isActive).length} نشط`}
            progress={75}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="إجمالي الكورسات"
            value={stats.totalCourses}
            icon={<VideoLibrary />}
            color="#4CAF50"
            subtitle={`${courses.filter(c => c.isActive).length} نشط`}
            progress={85}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="إجمالي الفيديوهات"
            value={stats.totalVideos}
            icon={<Category />}
            color="#FF9800"
            subtitle="عبر جميع الدورات"
            progress={100}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="إجمالي التسجيلات"
            value={stats.totalCertificates}
            icon={<WorkspacePremium />}
            color="#9C27B0"
            subtitle="في الدورات"
            progress={60}
          />
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Recent Activities */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                النشاطات الأخيرة
              </Typography>
              <List>
                {recentActivities.map((activity, index) => (
                  <ListItem
                    key={activity.id}
                    sx={{
                      borderRadius: 2,
                      mb: 1,
                      bgcolor: index % 2 === 0 ? 'grey.50' : 'transparent'
                    }}
                  >
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: activity.color }}>
                        {activity.icon}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={activity.title}
                      secondary={
                        <Box>
                          <Typography variant="body2" color="textSecondary">
                            {activity.description}
                          </Typography>
                          <Typography variant="caption" color="textSecondary">
                            {activity.time}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                إجراءات سريعة
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Paper
                  sx={{
                    p: 2,
                    cursor: 'pointer',
                    transition: 'all 0.3s',
                    '&:hover': {
                      bgcolor: 'primary.light',
                      color: 'white',
                      transform: 'translateY(-2px)'
                    }
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <School />
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      إضافة طالب جديد
                    </Typography>
                  </Box>
                </Paper>

                <Paper
                  sx={{
                    p: 2,
                    cursor: 'pointer',
                    transition: 'all 0.3s',
                    '&:hover': {
                      bgcolor: 'secondary.light',
                      color: 'white',
                      transform: 'translateY(-2px)'
                    }
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <VideoLibrary />
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      إنشاء كورس جديد
                    </Typography>
                  </Box>
                </Paper>

                <Paper
                  sx={{
                    p: 2,
                    cursor: 'pointer',
                    transition: 'all 0.3s',
                    '&:hover': {
                      bgcolor: 'success.light',
                      color: 'white',
                      transform: 'translateY(-2px)'
                    }
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <WorkspacePremium />
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      إصدار شهادة
                    </Typography>
                  </Box>
                </Paper>
              </Box>
            </CardContent>
          </Card>

          {/* System Status */}
          <Card sx={{ mt: 3 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                حالة النظام
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body2">الخادم</Typography>
                  <Chip label="متصل" color="success" size="small" />
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body2">قاعدة البيانات</Typography>
                  <Chip label="متصل" color="success" size="small" />
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body2">التخزين</Typography>
                  <Chip label="75% مستخدم" color="warning" size="small" />
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardOverview;

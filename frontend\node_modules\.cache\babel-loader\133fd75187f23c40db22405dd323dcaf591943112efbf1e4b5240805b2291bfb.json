{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0643\\u0648\\u0633\\u0627\\u062A\\\\frontend\\\\src\\\\components\\\\admin\\\\CourseManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Button, Card, CardContent, Grid, Dialog, DialogTitle, DialogContent, DialogActions, TextField, IconButton, Chip, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Switch, FormControlLabel, Select, MenuItem, FormControl, InputLabel, List, ListItem, ListItemText, ListItemSecondaryAction, Checkbox } from '@mui/material';\nimport { Add, Edit, Delete, VideoLibrary, People, Visibility, VisibilityOff } from '@mui/icons-material';\nimport axios from 'axios';\nimport VideoManagement from './VideoManagement';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CourseManagement = () => {\n  _s();\n  var _selectedCourse$enrol;\n  const [courses, setCourses] = useState([]);\n  const [students, setStudents] = useState([]);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [openStudentsDialog, setOpenStudentsDialog] = useState(false);\n  const [selectedCourse, setSelectedCourse] = useState(null);\n  const [editingCourse, setEditingCourse] = useState(null);\n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    price: '',\n    isActive: true,\n    level: 'مبتدئ',\n    duration: '',\n    category: ''\n  });\n  useEffect(() => {\n    fetchCourses();\n    fetchStudents();\n  }, []);\n  const fetchCourses = async () => {\n    try {\n      const response = await axios.get('/admin/courses');\n      setCourses(response.data);\n    } catch (error) {\n      console.error('خطأ في جلب الدورات:', error);\n      // بيانات تجريبية\n      setCourses([{\n        _id: '1',\n        title: 'أساسيات التسويق الرقمي',\n        description: 'تعلم أساسيات التسويق الرقمي من الصفر',\n        price: 299,\n        isActive: true,\n        level: 'مبتدئ',\n        duration: '8 ساعات',\n        category: 'التسويق الرقمي',\n        videos: [],\n        enrolledStudents: ['2', '3']\n      }, {\n        _id: '2',\n        title: 'إدارة وسائل التواصل الاجتماعي',\n        description: 'كيفية إدارة حسابات التواصل الاجتماعي بفعالية',\n        price: 399,\n        isActive: true,\n        level: 'متوسط',\n        duration: '6 ساعات',\n        category: 'وسائل التواصل',\n        videos: [],\n        enrolledStudents: ['2']\n      }]);\n    }\n  };\n  const fetchStudents = async () => {\n    try {\n      const response = await axios.get('/admin/students');\n      setStudents(response.data);\n    } catch (error) {\n      console.error('خطأ في جلب الطلاب:', error);\n      setStudents([{\n        _id: '2',\n        name: 'أحمد محمد',\n        studentCode: '123456',\n        isActive: true\n      }, {\n        _id: '3',\n        name: 'فاطمة علي',\n        studentCode: '789012',\n        isActive: true\n      }]);\n    }\n  };\n  const handleOpenDialog = (course = null) => {\n    if (course) {\n      setEditingCourse(course);\n      setFormData({\n        title: course.title,\n        description: course.description,\n        price: course.price,\n        isActive: course.isActive,\n        level: course.level || 'مبتدئ',\n        duration: course.duration || '',\n        category: course.category || ''\n      });\n    } else {\n      setEditingCourse(null);\n      setFormData({\n        title: '',\n        description: '',\n        price: '',\n        isActive: true,\n        level: 'مبتدئ',\n        duration: '',\n        category: ''\n      });\n    }\n    setOpenDialog(true);\n  };\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setEditingCourse(null);\n    setFormData({\n      title: '',\n      description: '',\n      price: '',\n      isActive: true\n    });\n  };\n  const handleSubmit = async () => {\n    try {\n      if (editingCourse) {\n        // تحديث دورة موجودة\n        const response = await axios.put(`/admin/courses/${editingCourse._id}`, formData);\n        setCourses(courses.map(course => course._id === editingCourse._id ? response.data : course));\n      } else {\n        // إضافة دورة جديدة\n        const response = await axios.post('/admin/courses', formData);\n        setCourses([...courses, response.data]);\n      }\n      handleCloseDialog();\n    } catch (error) {\n      console.error('خطأ في حفظ الدورة:', error);\n      // محاكاة النجاح للاختبار\n      const newCourse = {\n        _id: Date.now().toString(),\n        ...formData,\n        videos: [],\n        enrolledStudents: 0\n      };\n      if (editingCourse) {\n        setCourses(courses.map(course => course._id === editingCourse._id ? {\n          ...editingCourse,\n          ...formData\n        } : course));\n      } else {\n        setCourses([...courses, newCourse]);\n      }\n      handleCloseDialog();\n    }\n  };\n  const handleDelete = async courseId => {\n    if (window.confirm('هل أنت متأكد من حذف هذه الدورة؟')) {\n      try {\n        await axios.delete(`/admin/courses/${courseId}`);\n        setCourses(courses.filter(course => course._id !== courseId));\n      } catch (error) {\n        console.error('خطأ في حذف الدورة:', error);\n        // محاكاة النجاح للاختبار\n        setCourses(courses.filter(course => course._id !== courseId));\n      }\n    }\n  };\n  const toggleCourseStatus = async (courseId, currentStatus) => {\n    try {\n      const response = await axios.patch(`/admin/courses/${courseId}/status`, {\n        isActive: !currentStatus\n      });\n      setCourses(courses.map(course => course._id === courseId ? {\n        ...course,\n        isActive: !currentStatus\n      } : course));\n    } catch (error) {\n      console.error('خطأ في تغيير حالة الدورة:', error);\n      // محاكاة النجاح للاختبار\n      setCourses(courses.map(course => course._id === courseId ? {\n        ...course,\n        isActive: !currentStatus\n      } : course));\n    }\n  };\n  const handleManageStudents = course => {\n    setSelectedCourse(course);\n    setOpenStudentsDialog(true);\n  };\n  const handleEnrollStudent = async studentId => {\n    try {\n      await axios.post(`/admin/students/${studentId}/enroll/${selectedCourse._id}`);\n\n      // تحديث البيانات المحلية\n      setSelectedCourse(prev => ({\n        ...prev,\n        enrolledStudents: [...prev.enrolledStudents, studentId]\n      }));\n      setCourses(courses.map(course => course._id === selectedCourse._id ? {\n        ...course,\n        enrolledStudents: [...course.enrolledStudents, studentId]\n      } : course));\n      alert('تم تسجيل الطالب في الكورس بنجاح');\n    } catch (error) {\n      console.error('خطأ في تسجيل الطالب:', error);\n      alert('حدث خطأ في تسجيل الطالب');\n    }\n  };\n  const handleUnenrollStudent = async studentId => {\n    try {\n      await axios.delete(`/admin/students/${studentId}/unenroll/${selectedCourse._id}`);\n\n      // تحديث البيانات المحلية\n      setSelectedCourse(prev => ({\n        ...prev,\n        enrolledStudents: prev.enrolledStudents.filter(id => id !== studentId)\n      }));\n      setCourses(courses.map(course => course._id === selectedCourse._id ? {\n        ...course,\n        enrolledStudents: course.enrolledStudents.filter(id => id !== studentId)\n      } : course));\n      alert('تم إلغاء تسجيل الطالب من الكورس بنجاح');\n    } catch (error) {\n      console.error('خطأ في إلغاء تسجيل الطالب:', error);\n      alert('حدث خطأ في إلغاء تسجيل الطالب');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          fontWeight: 'bold'\n        },\n        children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u062F\\u0648\\u0631\\u0627\\u062A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(Add, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 22\n        }, this),\n        onClick: () => handleOpenDialog(),\n        sx: {\n          borderRadius: 2\n        },\n        children: \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u062F\\u0648\\u0631\\u0629 \\u062C\\u062F\\u064A\\u062F\\u0629\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            bgcolor: '#e3f2fd'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              sx: {\n                fontWeight: 'bold',\n                color: '#1976d2'\n              },\n              children: courses.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"textSecondary\",\n              children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u062F\\u0648\\u0631\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            bgcolor: '#e8f5e8'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              sx: {\n                fontWeight: 'bold',\n                color: '#4caf50'\n              },\n              children: courses.filter(c => c.isActive).length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"textSecondary\",\n              children: \"\\u0627\\u0644\\u062F\\u0648\\u0631\\u0627\\u062A \\u0627\\u0644\\u0646\\u0634\\u0637\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            bgcolor: '#fff3e0'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              sx: {\n                fontWeight: 'bold',\n                color: '#ff9800'\n              },\n              children: courses.reduce((total, course) => total + (course.enrolledStudents || 0), 0)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"textSecondary\",\n              children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0633\\u062C\\u0644\\u064A\\u0646\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            bgcolor: '#f3e5f5'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              sx: {\n                fontWeight: 'bold',\n                color: '#9c27b0'\n              },\n              children: courses.reduce((total, course) => {\n                var _course$videos;\n                return total + (((_course$videos = course.videos) === null || _course$videos === void 0 ? void 0 : _course$videos.length) || 0);\n              }, 0)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"textSecondary\",\n              children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\\u0647\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      sx: {\n        borderRadius: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            sx: {\n              bgcolor: '#f5f5f5'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold'\n              },\n              children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u062F\\u0648\\u0631\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold'\n              },\n              children: \"\\u0627\\u0644\\u0648\\u0635\\u0641\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold'\n              },\n              children: \"\\u0627\\u0644\\u0633\\u0639\\u0631\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold'\n              },\n              children: \"\\u0627\\u0644\\u0645\\u0633\\u062A\\u0648\\u0649\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold'\n              },\n              children: \"\\u0627\\u0644\\u0645\\u0633\\u062C\\u0644\\u064A\\u0646\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold'\n              },\n              children: \"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold'\n              },\n              children: \"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: courses.map(course => /*#__PURE__*/_jsxDEV(TableRow, {\n            hover: true,\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(VideoLibrary, {\n                  sx: {\n                    mr: 1,\n                    color: '#1976d2'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  sx: {\n                    fontWeight: 'medium'\n                  },\n                  children: course.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"textSecondary\",\n                children: course.description.length > 50 ? `${course.description.substring(0, 50)}...` : course.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                sx: {\n                  fontWeight: 'medium'\n                },\n                children: [course.price, \" \\u0631\\u064A\\u0627\\u0644\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: course.level || 'مبتدئ',\n                size: \"small\",\n                color: course.level === 'متقدم' ? 'error' : course.level === 'متوسط' ? 'warning' : 'success'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(People, {\n                  sx: {\n                    mr: 1,\n                    color: '#4caf50',\n                    fontSize: 20\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: Array.isArray(course.enrolledStudents) ? course.enrolledStudents.length : 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                control: /*#__PURE__*/_jsxDEV(Switch, {\n                  checked: course.isActive,\n                  onChange: () => toggleCourseStatus(course._id, course.isActive),\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 23\n                }, this),\n                label: course.isActive ? 'نشط' : 'غير نشط'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  gap: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => handleManageStudents(course),\n                  sx: {\n                    color: '#4caf50'\n                  },\n                  title: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0637\\u0644\\u0627\\u0628\",\n                  children: /*#__PURE__*/_jsxDEV(People, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 415,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => handleOpenDialog(course),\n                  sx: {\n                    color: '#1976d2'\n                  },\n                  title: \"\\u062A\\u0639\\u062F\\u064A\\u0644\",\n                  children: /*#__PURE__*/_jsxDEV(Edit, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 423,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 417,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => handleDelete(course._id),\n                  sx: {\n                    color: '#d32f2f'\n                  },\n                  title: \"\\u062D\\u0630\\u0641\",\n                  children: /*#__PURE__*/_jsxDEV(Delete, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 431,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 17\n            }, this)]\n          }, course._id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 344,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: handleCloseDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: editingCourse ? 'تعديل الدورة' : 'إضافة دورة جديدة'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 443,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u062F\\u0648\\u0631\\u0629\",\n            value: formData.title,\n            onChange: e => setFormData({\n              ...formData,\n              title: e.target.value\n            }),\n            sx: {\n              mb: 3\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"\\u0648\\u0635\\u0641 \\u0627\\u0644\\u062F\\u0648\\u0631\\u0629\",\n            multiline: true,\n            rows: 4,\n            value: formData.description,\n            onChange: e => setFormData({\n              ...formData,\n              description: e.target.value\n            }),\n            sx: {\n              mb: 3\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"\\u0627\\u0644\\u0633\\u0639\\u0631 (\\u0631\\u064A\\u0627\\u0644)\",\n            type: \"number\",\n            value: formData.price,\n            onChange: e => setFormData({\n              ...formData,\n              price: e.target.value\n            }),\n            sx: {\n              mb: 3\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              gap: 2,\n              mb: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"\\u0627\\u0644\\u0645\\u0633\\u062A\\u0648\\u0649\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.level,\n                label: \"\\u0627\\u0644\\u0645\\u0633\\u062A\\u0648\\u0649\",\n                onChange: e => setFormData({\n                  ...formData,\n                  level: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\\u0645\\u0628\\u062A\\u062F\\u0626\",\n                  children: \"\\u0645\\u0628\\u062A\\u062F\\u0626\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\\u0645\\u062A\\u0648\\u0633\\u0637\",\n                  children: \"\\u0645\\u062A\\u0648\\u0633\\u0637\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\\u0645\\u062A\\u0642\\u062F\\u0645\",\n                  children: \"\\u0645\\u062A\\u0642\\u062F\\u0645\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 478,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"\\u0627\\u0644\\u0645\\u062F\\u0629 (\\u0645\\u062B\\u0627\\u0644: 8 \\u0633\\u0627\\u0639\\u0627\\u062A)\",\n              value: formData.duration,\n              onChange: e => setFormData({\n                ...formData,\n                duration: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"\\u0627\\u0644\\u0641\\u0626\\u0629\",\n            value: formData.category,\n            onChange: e => setFormData({\n              ...formData,\n              category: e.target.value\n            }),\n            sx: {\n              mb: 3\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Switch, {\n              checked: formData.isActive,\n              onChange: e => setFormData({\n                ...formData,\n                isActive: e.target.checked\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 17\n            }, this),\n            label: \"\\u062F\\u0648\\u0631\\u0629 \\u0646\\u0634\\u0637\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 446,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDialog,\n          children: \"\\u0625\\u0644\\u063A\\u0627\\u0621\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 517,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmit,\n          variant: \"contained\",\n          children: editingCourse ? 'تحديث' : 'إضافة'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 516,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 442,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openStudentsDialog,\n      onClose: () => setOpenStudentsDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [\"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0637\\u0644\\u0627\\u0628 - \", selectedCourse === null || selectedCourse === void 0 ? void 0 : selectedCourse.title]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 531,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 2\n          },\n          children: [\"\\u0627\\u0644\\u0637\\u0644\\u0627\\u0628 \\u0627\\u0644\\u0645\\u0633\\u062C\\u0644\\u064A\\u0646 (\", (selectedCourse === null || selectedCourse === void 0 ? void 0 : (_selectedCourse$enrol = selectedCourse.enrolledStudents) === null || _selectedCourse$enrol === void 0 ? void 0 : _selectedCourse$enrol.length) || 0, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          sx: {\n            mb: 3\n          },\n          children: students.filter(student => {\n            var _selectedCourse$enrol2;\n            return selectedCourse === null || selectedCourse === void 0 ? void 0 : (_selectedCourse$enrol2 = selectedCourse.enrolledStudents) === null || _selectedCourse$enrol2 === void 0 ? void 0 : _selectedCourse$enrol2.includes(student._id);\n          }).map(student => /*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: student.name,\n              secondary: `كود الطالب: ${student.studentCode}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                color: \"error\",\n                size: \"small\",\n                onClick: () => handleUnenrollStudent(student._id),\n                children: \"\\u0625\\u0644\\u063A\\u0627\\u0621 \\u0627\\u0644\\u062A\\u0633\\u062C\\u064A\\u0644\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 19\n            }, this)]\n          }, student._id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 2\n          },\n          children: \"\\u0627\\u0644\\u0637\\u0644\\u0627\\u0628 \\u0627\\u0644\\u0645\\u062A\\u0627\\u062D\\u064A\\u0646 \\u0644\\u0644\\u062A\\u0633\\u062C\\u064A\\u0644\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          children: students.filter(student => {\n            var _selectedCourse$enrol3;\n            return !(selectedCourse !== null && selectedCourse !== void 0 && (_selectedCourse$enrol3 = selectedCourse.enrolledStudents) !== null && _selectedCourse$enrol3 !== void 0 && _selectedCourse$enrol3.includes(student._id));\n          }).map(student => /*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: student.name,\n              secondary: `كود الطالب: ${student.studentCode}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 571,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"primary\",\n                size: \"small\",\n                onClick: () => handleEnrollStudent(student._id),\n                children: \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0641\\u064A \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 575,\n              columnNumber: 19\n            }, this)]\n          }, student._id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 566,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 534,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setOpenStudentsDialog(false),\n          children: \"\\u0625\\u063A\\u0644\\u0627\\u0642\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 590,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 589,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 525,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 273,\n    columnNumber: 5\n  }, this);\n};\n_s(CourseManagement, \"744Ay+GUuCp+zuTUEYdLqUUjGMs=\");\n_c = CourseManagement;\nexport default CourseManagement;\nvar _c;\n$RefreshReg$(_c, \"CourseManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "IconButton", "Chip", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Switch", "FormControlLabel", "Select", "MenuItem", "FormControl", "InputLabel", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "Checkbox", "Add", "Edit", "Delete", "VideoLibrary", "People", "Visibility", "VisibilityOff", "axios", "VideoManagement", "jsxDEV", "_jsxDEV", "CourseManagement", "_s", "_selectedCourse$enrol", "courses", "setCourses", "students", "setStudents", "openDialog", "setOpenDialog", "openStudentsDialog", "setOpenStudentsDialog", "selectedCourse", "setSelectedCourse", "editingCourse", "setEditingCourse", "formData", "setFormData", "title", "description", "price", "isActive", "level", "duration", "category", "fetchCourses", "fetchStudents", "response", "get", "data", "error", "console", "_id", "videos", "enrolledStudents", "name", "studentCode", "handleOpenDialog", "course", "handleCloseDialog", "handleSubmit", "put", "map", "post", "newCourse", "Date", "now", "toString", "handleDelete", "courseId", "window", "confirm", "delete", "filter", "toggleCourseStatus", "currentStatus", "patch", "handleManageStudents", "handleEnrollStudent", "studentId", "prev", "alert", "handleUnenrollStudent", "id", "children", "sx", "display", "justifyContent", "alignItems", "mb", "variant", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "startIcon", "onClick", "borderRadius", "container", "spacing", "item", "xs", "sm", "md", "bgcolor", "color", "length", "c", "reduce", "total", "_course$videos", "component", "hover", "mr", "substring", "label", "size", "fontSize", "Array", "isArray", "control", "checked", "onChange", "gap", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "pt", "value", "e", "target", "multiline", "rows", "type", "student", "_selectedCourse$enrol2", "includes", "primary", "secondary", "_selectedCourse$enrol3", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/كوسات/frontend/src/components/admin/CourseManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON>,\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>,\n  Card,\n  CardContent,\n  Grid,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  IconButton,\n  Chip,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Switch,\n  FormControlLabel,\n  Select,\n  MenuItem,\n  FormControl,\n  InputLabel,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction,\n  Checkbox\n} from '@mui/material';\nimport {\n  Add,\n  Edit,\n  Delete,\n  VideoLibrary,\n  People,\n  Visibility,\n  VisibilityOff\n} from '@mui/icons-material';\nimport axios from 'axios';\nimport VideoManagement from './VideoManagement';\n\nconst CourseManagement = () => {\n  const [courses, setCourses] = useState([]);\n  const [students, setStudents] = useState([]);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [openStudentsDialog, setOpenStudentsDialog] = useState(false);\n  const [selectedCourse, setSelectedCourse] = useState(null);\n  const [editingCourse, setEditingCourse] = useState(null);\n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    price: '',\n    isActive: true,\n    level: 'مبتدئ',\n    duration: '',\n    category: ''\n  });\n\n  useEffect(() => {\n    fetchCourses();\n    fetchStudents();\n  }, []);\n\n  const fetchCourses = async () => {\n    try {\n      const response = await axios.get('/admin/courses');\n      setCourses(response.data);\n    } catch (error) {\n      console.error('خطأ في جلب الدورات:', error);\n      // بيانات تجريبية\n      setCourses([\n        {\n          _id: '1',\n          title: 'أساسيات التسويق الرقمي',\n          description: 'تعلم أساسيات التسويق الرقمي من الصفر',\n          price: 299,\n          isActive: true,\n          level: 'مبتدئ',\n          duration: '8 ساعات',\n          category: 'التسويق الرقمي',\n          videos: [],\n          enrolledStudents: ['2', '3']\n        },\n        {\n          _id: '2',\n          title: 'إدارة وسائل التواصل الاجتماعي',\n          description: 'كيفية إدارة حسابات التواصل الاجتماعي بفعالية',\n          price: 399,\n          isActive: true,\n          level: 'متوسط',\n          duration: '6 ساعات',\n          category: 'وسائل التواصل',\n          videos: [],\n          enrolledStudents: ['2']\n        }\n      ]);\n    }\n  };\n\n  const fetchStudents = async () => {\n    try {\n      const response = await axios.get('/admin/students');\n      setStudents(response.data);\n    } catch (error) {\n      console.error('خطأ في جلب الطلاب:', error);\n      setStudents([\n        { _id: '2', name: 'أحمد محمد', studentCode: '123456', isActive: true },\n        { _id: '3', name: 'فاطمة علي', studentCode: '789012', isActive: true }\n      ]);\n    }\n  };\n\n  const handleOpenDialog = (course = null) => {\n    if (course) {\n      setEditingCourse(course);\n      setFormData({\n        title: course.title,\n        description: course.description,\n        price: course.price,\n        isActive: course.isActive,\n        level: course.level || 'مبتدئ',\n        duration: course.duration || '',\n        category: course.category || ''\n      });\n    } else {\n      setEditingCourse(null);\n      setFormData({\n        title: '',\n        description: '',\n        price: '',\n        isActive: true,\n        level: 'مبتدئ',\n        duration: '',\n        category: ''\n      });\n    }\n    setOpenDialog(true);\n  };\n\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setEditingCourse(null);\n    setFormData({\n      title: '',\n      description: '',\n      price: '',\n      isActive: true\n    });\n  };\n\n  const handleSubmit = async () => {\n    try {\n      if (editingCourse) {\n        // تحديث دورة موجودة\n        const response = await axios.put(`/admin/courses/${editingCourse._id}`, formData);\n        setCourses(courses.map(course => \n          course._id === editingCourse._id ? response.data : course\n        ));\n      } else {\n        // إضافة دورة جديدة\n        const response = await axios.post('/admin/courses', formData);\n        setCourses([...courses, response.data]);\n      }\n      handleCloseDialog();\n    } catch (error) {\n      console.error('خطأ في حفظ الدورة:', error);\n      // محاكاة النجاح للاختبار\n      const newCourse = {\n        _id: Date.now().toString(),\n        ...formData,\n        videos: [],\n        enrolledStudents: 0\n      };\n      \n      if (editingCourse) {\n        setCourses(courses.map(course => \n          course._id === editingCourse._id ? { ...editingCourse, ...formData } : course\n        ));\n      } else {\n        setCourses([...courses, newCourse]);\n      }\n      handleCloseDialog();\n    }\n  };\n\n  const handleDelete = async (courseId) => {\n    if (window.confirm('هل أنت متأكد من حذف هذه الدورة؟')) {\n      try {\n        await axios.delete(`/admin/courses/${courseId}`);\n        setCourses(courses.filter(course => course._id !== courseId));\n      } catch (error) {\n        console.error('خطأ في حذف الدورة:', error);\n        // محاكاة النجاح للاختبار\n        setCourses(courses.filter(course => course._id !== courseId));\n      }\n    }\n  };\n\n  const toggleCourseStatus = async (courseId, currentStatus) => {\n    try {\n      const response = await axios.patch(`/admin/courses/${courseId}/status`, {\n        isActive: !currentStatus\n      });\n      setCourses(courses.map(course => \n        course._id === courseId ? { ...course, isActive: !currentStatus } : course\n      ));\n    } catch (error) {\n      console.error('خطأ في تغيير حالة الدورة:', error);\n      // محاكاة النجاح للاختبار\n      setCourses(courses.map(course => \n        course._id === courseId ? { ...course, isActive: !currentStatus } : course\n      ));\n    }\n  };\n\n  const handleManageStudents = (course) => {\n    setSelectedCourse(course);\n    setOpenStudentsDialog(true);\n  };\n\n  const handleEnrollStudent = async (studentId) => {\n    try {\n      await axios.post(`/admin/students/${studentId}/enroll/${selectedCourse._id}`);\n\n      // تحديث البيانات المحلية\n      setSelectedCourse(prev => ({\n        ...prev,\n        enrolledStudents: [...prev.enrolledStudents, studentId]\n      }));\n\n      setCourses(courses.map(course =>\n        course._id === selectedCourse._id\n          ? { ...course, enrolledStudents: [...course.enrolledStudents, studentId] }\n          : course\n      ));\n\n      alert('تم تسجيل الطالب في الكورس بنجاح');\n    } catch (error) {\n      console.error('خطأ في تسجيل الطالب:', error);\n      alert('حدث خطأ في تسجيل الطالب');\n    }\n  };\n\n  const handleUnenrollStudent = async (studentId) => {\n    try {\n      await axios.delete(`/admin/students/${studentId}/unenroll/${selectedCourse._id}`);\n\n      // تحديث البيانات المحلية\n      setSelectedCourse(prev => ({\n        ...prev,\n        enrolledStudents: prev.enrolledStudents.filter(id => id !== studentId)\n      }));\n\n      setCourses(courses.map(course =>\n        course._id === selectedCourse._id\n          ? { ...course, enrolledStudents: course.enrolledStudents.filter(id => id !== studentId) }\n          : course\n      ));\n\n      alert('تم إلغاء تسجيل الطالب من الكورس بنجاح');\n    } catch (error) {\n      console.error('خطأ في إلغاء تسجيل الطالب:', error);\n      alert('حدث خطأ في إلغاء تسجيل الطالب');\n    }\n  };\n\n  return (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h4\" sx={{ fontWeight: 'bold' }}>\n          إدارة الدورات\n        </Typography>\n        <Button\n          variant=\"contained\"\n          startIcon={<Add />}\n          onClick={() => handleOpenDialog()}\n          sx={{ borderRadius: 2 }}\n        >\n          إضافة دورة جديدة\n        </Button>\n      </Box>\n\n      {/* إحصائيات سريعة */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card sx={{ bgcolor: '#e3f2fd' }}>\n            <CardContent>\n              <Typography variant=\"h4\" sx={{ fontWeight: 'bold', color: '#1976d2' }}>\n                {courses.length}\n              </Typography>\n              <Typography variant=\"body2\" color=\"textSecondary\">\n                إجمالي الدورات\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        \n        <Grid item xs={12} sm={6} md={3}>\n          <Card sx={{ bgcolor: '#e8f5e8' }}>\n            <CardContent>\n              <Typography variant=\"h4\" sx={{ fontWeight: 'bold', color: '#4caf50' }}>\n                {courses.filter(c => c.isActive).length}\n              </Typography>\n              <Typography variant=\"body2\" color=\"textSecondary\">\n                الدورات النشطة\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        \n        <Grid item xs={12} sm={6} md={3}>\n          <Card sx={{ bgcolor: '#fff3e0' }}>\n            <CardContent>\n              <Typography variant=\"h4\" sx={{ fontWeight: 'bold', color: '#ff9800' }}>\n                {courses.reduce((total, course) => total + (course.enrolledStudents || 0), 0)}\n              </Typography>\n              <Typography variant=\"body2\" color=\"textSecondary\">\n                إجمالي المسجلين\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        \n        <Grid item xs={12} sm={6} md={3}>\n          <Card sx={{ bgcolor: '#f3e5f5' }}>\n            <CardContent>\n              <Typography variant=\"h4\" sx={{ fontWeight: 'bold', color: '#9c27b0' }}>\n                {courses.reduce((total, course) => total + (course.videos?.length || 0), 0)}\n              </Typography>\n              <Typography variant=\"body2\" color=\"textSecondary\">\n                إجمالي الفيديوهات\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* جدول الدورات */}\n      <TableContainer component={Paper} sx={{ borderRadius: 2 }}>\n        <Table>\n          <TableHead>\n            <TableRow sx={{ bgcolor: '#f5f5f5' }}>\n              <TableCell sx={{ fontWeight: 'bold' }}>اسم الدورة</TableCell>\n              <TableCell sx={{ fontWeight: 'bold' }}>الوصف</TableCell>\n              <TableCell sx={{ fontWeight: 'bold' }}>السعر</TableCell>\n              <TableCell sx={{ fontWeight: 'bold' }}>المستوى</TableCell>\n              <TableCell sx={{ fontWeight: 'bold' }}>المسجلين</TableCell>\n              <TableCell sx={{ fontWeight: 'bold' }}>الحالة</TableCell>\n              <TableCell sx={{ fontWeight: 'bold' }}>الإجراءات</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {courses.map((course) => (\n              <TableRow key={course._id} hover>\n                <TableCell>\n                  <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                    <VideoLibrary sx={{ mr: 1, color: '#1976d2' }} />\n                    <Typography variant=\"subtitle2\" sx={{ fontWeight: 'medium' }}>\n                      {course.title}\n                    </Typography>\n                  </Box>\n                </TableCell>\n                <TableCell>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    {course.description.length > 50 \n                      ? `${course.description.substring(0, 50)}...` \n                      : course.description}\n                  </Typography>\n                </TableCell>\n                <TableCell>\n                  <Typography variant=\"subtitle2\" sx={{ fontWeight: 'medium' }}>\n                    {course.price} ريال\n                  </Typography>\n                </TableCell>\n                <TableCell>\n                  <Chip\n                    label={course.level || 'مبتدئ'}\n                    size=\"small\"\n                    color={course.level === 'متقدم' ? 'error' : course.level === 'متوسط' ? 'warning' : 'success'}\n                  />\n                </TableCell>\n                <TableCell>\n                  <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                    <People sx={{ mr: 1, color: '#4caf50', fontSize: 20 }} />\n                    <Typography variant=\"body2\">\n                      {Array.isArray(course.enrolledStudents) ? course.enrolledStudents.length : 0}\n                    </Typography>\n                  </Box>\n                </TableCell>\n                <TableCell>\n                  <FormControlLabel\n                    control={\n                      <Switch\n                        checked={course.isActive}\n                        onChange={() => toggleCourseStatus(course._id, course.isActive)}\n                        color=\"primary\"\n                      />\n                    }\n                    label={course.isActive ? 'نشط' : 'غير نشط'}\n                  />\n                </TableCell>\n                <TableCell>\n                  <Box sx={{ display: 'flex', gap: 1 }}>\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => handleManageStudents(course)}\n                      sx={{ color: '#4caf50' }}\n                      title=\"إدارة الطلاب\"\n                    >\n                      <People />\n                    </IconButton>\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => handleOpenDialog(course)}\n                      sx={{ color: '#1976d2' }}\n                      title=\"تعديل\"\n                    >\n                      <Edit />\n                    </IconButton>\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => handleDelete(course._id)}\n                      sx={{ color: '#d32f2f' }}\n                      title=\"حذف\"\n                    >\n                      <Delete />\n                    </IconButton>\n                  </Box>\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n      </TableContainer>\n\n      {/* Dialog لإضافة/تعديل الدورة */}\n      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          {editingCourse ? 'تعديل الدورة' : 'إضافة دورة جديدة'}\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <TextField\n              fullWidth\n              label=\"اسم الدورة\"\n              value={formData.title}\n              onChange={(e) => setFormData({ ...formData, title: e.target.value })}\n              sx={{ mb: 3 }}\n            />\n            \n            <TextField\n              fullWidth\n              label=\"وصف الدورة\"\n              multiline\n              rows={4}\n              value={formData.description}\n              onChange={(e) => setFormData({ ...formData, description: e.target.value })}\n              sx={{ mb: 3 }}\n            />\n            \n            <TextField\n              fullWidth\n              label=\"السعر (ريال)\"\n              type=\"number\"\n              value={formData.price}\n              onChange={(e) => setFormData({ ...formData, price: e.target.value })}\n              sx={{ mb: 3 }}\n            />\n\n            <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>\n              <FormControl fullWidth>\n                <InputLabel>المستوى</InputLabel>\n                <Select\n                  value={formData.level}\n                  label=\"المستوى\"\n                  onChange={(e) => setFormData({ ...formData, level: e.target.value })}\n                >\n                  <MenuItem value=\"مبتدئ\">مبتدئ</MenuItem>\n                  <MenuItem value=\"متوسط\">متوسط</MenuItem>\n                  <MenuItem value=\"متقدم\">متقدم</MenuItem>\n                </Select>\n              </FormControl>\n\n              <TextField\n                fullWidth\n                label=\"المدة (مثال: 8 ساعات)\"\n                value={formData.duration}\n                onChange={(e) => setFormData({ ...formData, duration: e.target.value })}\n              />\n            </Box>\n\n            <TextField\n              fullWidth\n              label=\"الفئة\"\n              value={formData.category}\n              onChange={(e) => setFormData({ ...formData, category: e.target.value })}\n              sx={{ mb: 3 }}\n            />\n\n            <FormControlLabel\n              control={\n                <Switch\n                  checked={formData.isActive}\n                  onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}\n                />\n              }\n              label=\"دورة نشطة\"\n            />\n          </Box>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDialog}>إلغاء</Button>\n          <Button onClick={handleSubmit} variant=\"contained\">\n            {editingCourse ? 'تحديث' : 'إضافة'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog إدارة الطلاب */}\n      <Dialog\n        open={openStudentsDialog}\n        onClose={() => setOpenStudentsDialog(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          إدارة الطلاب - {selectedCourse?.title}\n        </DialogTitle>\n        <DialogContent>\n          <Typography variant=\"h6\" sx={{ mb: 2 }}>\n            الطلاب المسجلين ({selectedCourse?.enrolledStudents?.length || 0})\n          </Typography>\n\n          <List sx={{ mb: 3 }}>\n            {students\n              .filter(student => selectedCourse?.enrolledStudents?.includes(student._id))\n              .map((student) => (\n                <ListItem key={student._id}>\n                  <ListItemText\n                    primary={student.name}\n                    secondary={`كود الطالب: ${student.studentCode}`}\n                  />\n                  <ListItemSecondaryAction>\n                    <Button\n                      variant=\"outlined\"\n                      color=\"error\"\n                      size=\"small\"\n                      onClick={() => handleUnenrollStudent(student._id)}\n                    >\n                      إلغاء التسجيل\n                    </Button>\n                  </ListItemSecondaryAction>\n                </ListItem>\n              ))}\n          </List>\n\n          <Typography variant=\"h6\" sx={{ mb: 2 }}>\n            الطلاب المتاحين للتسجيل\n          </Typography>\n\n          <List>\n            {students\n              .filter(student => !selectedCourse?.enrolledStudents?.includes(student._id))\n              .map((student) => (\n                <ListItem key={student._id}>\n                  <ListItemText\n                    primary={student.name}\n                    secondary={`كود الطالب: ${student.studentCode}`}\n                  />\n                  <ListItemSecondaryAction>\n                    <Button\n                      variant=\"contained\"\n                      color=\"primary\"\n                      size=\"small\"\n                      onClick={() => handleEnrollStudent(student._id)}\n                    >\n                      تسجيل في الكورس\n                    </Button>\n                  </ListItemSecondaryAction>\n                </ListItem>\n              ))}\n          </List>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setOpenStudentsDialog(false)}>إغلاق</Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default CourseManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,gBAAgB,EAChBC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,QAAQ,QACH,eAAe;AACtB,SACEC,GAAG,EACHC,IAAI,EACJC,MAAM,EACNC,YAAY,EACZC,MAAM,EACNC,UAAU,EACVC,aAAa,QACR,qBAAqB;AAC5B,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAC7B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACiD,QAAQ,EAAEC,WAAW,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmD,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACqD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACuD,cAAc,EAAEC,iBAAiB,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACyD,aAAa,EAAEC,gBAAgB,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC2D,QAAQ,EAAEC,WAAW,CAAC,GAAG5D,QAAQ,CAAC;IACvC6D,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,IAAI;IACdC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEFlE,SAAS,CAAC,MAAM;IACdmE,YAAY,CAAC,CAAC;IACdC,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAME,QAAQ,GAAG,MAAM9B,KAAK,CAAC+B,GAAG,CAAC,gBAAgB,CAAC;MAClDvB,UAAU,CAACsB,QAAQ,CAACE,IAAI,CAAC;IAC3B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C;MACAzB,UAAU,CAAC,CACT;QACE2B,GAAG,EAAE,GAAG;QACRd,KAAK,EAAE,wBAAwB;QAC/BC,WAAW,EAAE,sCAAsC;QACnDC,KAAK,EAAE,GAAG;QACVC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,OAAO;QACdC,QAAQ,EAAE,SAAS;QACnBC,QAAQ,EAAE,gBAAgB;QAC1BS,MAAM,EAAE,EAAE;QACVC,gBAAgB,EAAE,CAAC,GAAG,EAAE,GAAG;MAC7B,CAAC,EACD;QACEF,GAAG,EAAE,GAAG;QACRd,KAAK,EAAE,+BAA+B;QACtCC,WAAW,EAAE,8CAA8C;QAC3DC,KAAK,EAAE,GAAG;QACVC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,OAAO;QACdC,QAAQ,EAAE,SAAS;QACnBC,QAAQ,EAAE,eAAe;QACzBS,MAAM,EAAE,EAAE;QACVC,gBAAgB,EAAE,CAAC,GAAG;MACxB,CAAC,CACF,CAAC;IACJ;EACF,CAAC;EAED,MAAMR,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM9B,KAAK,CAAC+B,GAAG,CAAC,iBAAiB,CAAC;MACnDrB,WAAW,CAACoB,QAAQ,CAACE,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CvB,WAAW,CAAC,CACV;QAAEyB,GAAG,EAAE,GAAG;QAAEG,IAAI,EAAE,WAAW;QAAEC,WAAW,EAAE,QAAQ;QAAEf,QAAQ,EAAE;MAAK,CAAC,EACtE;QAAEW,GAAG,EAAE,GAAG;QAAEG,IAAI,EAAE,WAAW;QAAEC,WAAW,EAAE,QAAQ;QAAEf,QAAQ,EAAE;MAAK,CAAC,CACvE,CAAC;IACJ;EACF,CAAC;EAED,MAAMgB,gBAAgB,GAAGA,CAACC,MAAM,GAAG,IAAI,KAAK;IAC1C,IAAIA,MAAM,EAAE;MACVvB,gBAAgB,CAACuB,MAAM,CAAC;MACxBrB,WAAW,CAAC;QACVC,KAAK,EAAEoB,MAAM,CAACpB,KAAK;QACnBC,WAAW,EAAEmB,MAAM,CAACnB,WAAW;QAC/BC,KAAK,EAAEkB,MAAM,CAAClB,KAAK;QACnBC,QAAQ,EAAEiB,MAAM,CAACjB,QAAQ;QACzBC,KAAK,EAAEgB,MAAM,CAAChB,KAAK,IAAI,OAAO;QAC9BC,QAAQ,EAAEe,MAAM,CAACf,QAAQ,IAAI,EAAE;QAC/BC,QAAQ,EAAEc,MAAM,CAACd,QAAQ,IAAI;MAC/B,CAAC,CAAC;IACJ,CAAC,MAAM;MACLT,gBAAgB,CAAC,IAAI,CAAC;MACtBE,WAAW,CAAC;QACVC,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,EAAE;QACfC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,OAAO;QACdC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IACAf,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAM8B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B9B,aAAa,CAAC,KAAK,CAAC;IACpBM,gBAAgB,CAAC,IAAI,CAAC;IACtBE,WAAW,CAAC;MACVC,KAAK,EAAE,EAAE;MACTC,WAAW,EAAE,EAAE;MACfC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;EAED,MAAMmB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,IAAI1B,aAAa,EAAE;QACjB;QACA,MAAMa,QAAQ,GAAG,MAAM9B,KAAK,CAAC4C,GAAG,CAAC,kBAAkB3B,aAAa,CAACkB,GAAG,EAAE,EAAEhB,QAAQ,CAAC;QACjFX,UAAU,CAACD,OAAO,CAACsC,GAAG,CAACJ,MAAM,IAC3BA,MAAM,CAACN,GAAG,KAAKlB,aAAa,CAACkB,GAAG,GAAGL,QAAQ,CAACE,IAAI,GAAGS,MACrD,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAMX,QAAQ,GAAG,MAAM9B,KAAK,CAAC8C,IAAI,CAAC,gBAAgB,EAAE3B,QAAQ,CAAC;QAC7DX,UAAU,CAAC,CAAC,GAAGD,OAAO,EAAEuB,QAAQ,CAACE,IAAI,CAAC,CAAC;MACzC;MACAU,iBAAiB,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C;MACA,MAAMc,SAAS,GAAG;QAChBZ,GAAG,EAAEa,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QAC1B,GAAG/B,QAAQ;QACXiB,MAAM,EAAE,EAAE;QACVC,gBAAgB,EAAE;MACpB,CAAC;MAED,IAAIpB,aAAa,EAAE;QACjBT,UAAU,CAACD,OAAO,CAACsC,GAAG,CAACJ,MAAM,IAC3BA,MAAM,CAACN,GAAG,KAAKlB,aAAa,CAACkB,GAAG,GAAG;UAAE,GAAGlB,aAAa;UAAE,GAAGE;QAAS,CAAC,GAAGsB,MACzE,CAAC,CAAC;MACJ,CAAC,MAAM;QACLjC,UAAU,CAAC,CAAC,GAAGD,OAAO,EAAEwC,SAAS,CAAC,CAAC;MACrC;MACAL,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,MAAMS,YAAY,GAAG,MAAOC,QAAQ,IAAK;IACvC,IAAIC,MAAM,CAACC,OAAO,CAAC,iCAAiC,CAAC,EAAE;MACrD,IAAI;QACF,MAAMtD,KAAK,CAACuD,MAAM,CAAC,kBAAkBH,QAAQ,EAAE,CAAC;QAChD5C,UAAU,CAACD,OAAO,CAACiD,MAAM,CAACf,MAAM,IAAIA,MAAM,CAACN,GAAG,KAAKiB,QAAQ,CAAC,CAAC;MAC/D,CAAC,CAAC,OAAOnB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC1C;QACAzB,UAAU,CAACD,OAAO,CAACiD,MAAM,CAACf,MAAM,IAAIA,MAAM,CAACN,GAAG,KAAKiB,QAAQ,CAAC,CAAC;MAC/D;IACF;EACF,CAAC;EAED,MAAMK,kBAAkB,GAAG,MAAAA,CAAOL,QAAQ,EAAEM,aAAa,KAAK;IAC5D,IAAI;MACF,MAAM5B,QAAQ,GAAG,MAAM9B,KAAK,CAAC2D,KAAK,CAAC,kBAAkBP,QAAQ,SAAS,EAAE;QACtE5B,QAAQ,EAAE,CAACkC;MACb,CAAC,CAAC;MACFlD,UAAU,CAACD,OAAO,CAACsC,GAAG,CAACJ,MAAM,IAC3BA,MAAM,CAACN,GAAG,KAAKiB,QAAQ,GAAG;QAAE,GAAGX,MAAM;QAAEjB,QAAQ,EAAE,CAACkC;MAAc,CAAC,GAAGjB,MACtE,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD;MACAzB,UAAU,CAACD,OAAO,CAACsC,GAAG,CAACJ,MAAM,IAC3BA,MAAM,CAACN,GAAG,KAAKiB,QAAQ,GAAG;QAAE,GAAGX,MAAM;QAAEjB,QAAQ,EAAE,CAACkC;MAAc,CAAC,GAAGjB,MACtE,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMmB,oBAAoB,GAAInB,MAAM,IAAK;IACvCzB,iBAAiB,CAACyB,MAAM,CAAC;IACzB3B,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAM+C,mBAAmB,GAAG,MAAOC,SAAS,IAAK;IAC/C,IAAI;MACF,MAAM9D,KAAK,CAAC8C,IAAI,CAAC,mBAAmBgB,SAAS,WAAW/C,cAAc,CAACoB,GAAG,EAAE,CAAC;;MAE7E;MACAnB,iBAAiB,CAAC+C,IAAI,KAAK;QACzB,GAAGA,IAAI;QACP1B,gBAAgB,EAAE,CAAC,GAAG0B,IAAI,CAAC1B,gBAAgB,EAAEyB,SAAS;MACxD,CAAC,CAAC,CAAC;MAEHtD,UAAU,CAACD,OAAO,CAACsC,GAAG,CAACJ,MAAM,IAC3BA,MAAM,CAACN,GAAG,KAAKpB,cAAc,CAACoB,GAAG,GAC7B;QAAE,GAAGM,MAAM;QAAEJ,gBAAgB,EAAE,CAAC,GAAGI,MAAM,CAACJ,gBAAgB,EAAEyB,SAAS;MAAE,CAAC,GACxErB,MACN,CAAC,CAAC;MAEFuB,KAAK,CAAC,iCAAiC,CAAC;IAC1C,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C+B,KAAK,CAAC,yBAAyB,CAAC;IAClC;EACF,CAAC;EAED,MAAMC,qBAAqB,GAAG,MAAOH,SAAS,IAAK;IACjD,IAAI;MACF,MAAM9D,KAAK,CAACuD,MAAM,CAAC,mBAAmBO,SAAS,aAAa/C,cAAc,CAACoB,GAAG,EAAE,CAAC;;MAEjF;MACAnB,iBAAiB,CAAC+C,IAAI,KAAK;QACzB,GAAGA,IAAI;QACP1B,gBAAgB,EAAE0B,IAAI,CAAC1B,gBAAgB,CAACmB,MAAM,CAACU,EAAE,IAAIA,EAAE,KAAKJ,SAAS;MACvE,CAAC,CAAC,CAAC;MAEHtD,UAAU,CAACD,OAAO,CAACsC,GAAG,CAACJ,MAAM,IAC3BA,MAAM,CAACN,GAAG,KAAKpB,cAAc,CAACoB,GAAG,GAC7B;QAAE,GAAGM,MAAM;QAAEJ,gBAAgB,EAAEI,MAAM,CAACJ,gBAAgB,CAACmB,MAAM,CAACU,EAAE,IAAIA,EAAE,KAAKJ,SAAS;MAAE,CAAC,GACvFrB,MACN,CAAC,CAAC;MAEFuB,KAAK,CAAC,uCAAuC,CAAC;IAChD,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD+B,KAAK,CAAC,+BAA+B,CAAC;IACxC;EACF,CAAC;EAED,oBACE7D,OAAA,CAACzC,GAAG;IAAAyG,QAAA,gBACFhE,OAAA,CAACzC,GAAG;MAAC0G,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACzFhE,OAAA,CAACxC,UAAU;QAAC8G,OAAO,EAAC,IAAI;QAACL,EAAE,EAAE;UAAEM,UAAU,EAAE;QAAO,CAAE;QAAAP,QAAA,EAAC;MAErD;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb3E,OAAA,CAACvC,MAAM;QACL6G,OAAO,EAAC,WAAW;QACnBM,SAAS,eAAE5E,OAAA,CAACV,GAAG;UAAAkF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACnBE,OAAO,EAAEA,CAAA,KAAMxC,gBAAgB,CAAC,CAAE;QAClC4B,EAAE,EAAE;UAAEa,YAAY,EAAE;QAAE,CAAE;QAAAd,QAAA,EACzB;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN3E,OAAA,CAACpC,IAAI;MAACmH,SAAS;MAACC,OAAO,EAAE,CAAE;MAACf,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACxChE,OAAA,CAACpC,IAAI;QAACqH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAApB,QAAA,eAC9BhE,OAAA,CAACtC,IAAI;UAACuG,EAAE,EAAE;YAAEoB,OAAO,EAAE;UAAU,CAAE;UAAArB,QAAA,eAC/BhE,OAAA,CAACrC,WAAW;YAAAqG,QAAA,gBACVhE,OAAA,CAACxC,UAAU;cAAC8G,OAAO,EAAC,IAAI;cAACL,EAAE,EAAE;gBAAEM,UAAU,EAAE,MAAM;gBAAEe,KAAK,EAAE;cAAU,CAAE;cAAAtB,QAAA,EACnE5D,OAAO,CAACmF;YAAM;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACb3E,OAAA,CAACxC,UAAU;cAAC8G,OAAO,EAAC,OAAO;cAACgB,KAAK,EAAC,eAAe;cAAAtB,QAAA,EAAC;YAElD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP3E,OAAA,CAACpC,IAAI;QAACqH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAApB,QAAA,eAC9BhE,OAAA,CAACtC,IAAI;UAACuG,EAAE,EAAE;YAAEoB,OAAO,EAAE;UAAU,CAAE;UAAArB,QAAA,eAC/BhE,OAAA,CAACrC,WAAW;YAAAqG,QAAA,gBACVhE,OAAA,CAACxC,UAAU;cAAC8G,OAAO,EAAC,IAAI;cAACL,EAAE,EAAE;gBAAEM,UAAU,EAAE,MAAM;gBAAEe,KAAK,EAAE;cAAU,CAAE;cAAAtB,QAAA,EACnE5D,OAAO,CAACiD,MAAM,CAACmC,CAAC,IAAIA,CAAC,CAACnE,QAAQ,CAAC,CAACkE;YAAM;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACb3E,OAAA,CAACxC,UAAU;cAAC8G,OAAO,EAAC,OAAO;cAACgB,KAAK,EAAC,eAAe;cAAAtB,QAAA,EAAC;YAElD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP3E,OAAA,CAACpC,IAAI;QAACqH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAApB,QAAA,eAC9BhE,OAAA,CAACtC,IAAI;UAACuG,EAAE,EAAE;YAAEoB,OAAO,EAAE;UAAU,CAAE;UAAArB,QAAA,eAC/BhE,OAAA,CAACrC,WAAW;YAAAqG,QAAA,gBACVhE,OAAA,CAACxC,UAAU;cAAC8G,OAAO,EAAC,IAAI;cAACL,EAAE,EAAE;gBAAEM,UAAU,EAAE,MAAM;gBAAEe,KAAK,EAAE;cAAU,CAAE;cAAAtB,QAAA,EACnE5D,OAAO,CAACqF,MAAM,CAAC,CAACC,KAAK,EAAEpD,MAAM,KAAKoD,KAAK,IAAIpD,MAAM,CAACJ,gBAAgB,IAAI,CAAC,CAAC,EAAE,CAAC;YAAC;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,eACb3E,OAAA,CAACxC,UAAU;cAAC8G,OAAO,EAAC,OAAO;cAACgB,KAAK,EAAC,eAAe;cAAAtB,QAAA,EAAC;YAElD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP3E,OAAA,CAACpC,IAAI;QAACqH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAApB,QAAA,eAC9BhE,OAAA,CAACtC,IAAI;UAACuG,EAAE,EAAE;YAAEoB,OAAO,EAAE;UAAU,CAAE;UAAArB,QAAA,eAC/BhE,OAAA,CAACrC,WAAW;YAAAqG,QAAA,gBACVhE,OAAA,CAACxC,UAAU;cAAC8G,OAAO,EAAC,IAAI;cAACL,EAAE,EAAE;gBAAEM,UAAU,EAAE,MAAM;gBAAEe,KAAK,EAAE;cAAU,CAAE;cAAAtB,QAAA,EACnE5D,OAAO,CAACqF,MAAM,CAAC,CAACC,KAAK,EAAEpD,MAAM;gBAAA,IAAAqD,cAAA;gBAAA,OAAKD,KAAK,IAAI,EAAAC,cAAA,GAAArD,MAAM,CAACL,MAAM,cAAA0D,cAAA,uBAAbA,cAAA,CAAeJ,MAAM,KAAI,CAAC,CAAC;cAAA,GAAE,CAAC;YAAC;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC,eACb3E,OAAA,CAACxC,UAAU;cAAC8G,OAAO,EAAC,OAAO;cAACgB,KAAK,EAAC,eAAe;cAAAtB,QAAA,EAAC;YAElD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP3E,OAAA,CAACzB,cAAc;MAACqH,SAAS,EAAElH,KAAM;MAACuF,EAAE,EAAE;QAAEa,YAAY,EAAE;MAAE,CAAE;MAAAd,QAAA,eACxDhE,OAAA,CAAC5B,KAAK;QAAA4F,QAAA,gBACJhE,OAAA,CAACxB,SAAS;UAAAwF,QAAA,eACRhE,OAAA,CAACvB,QAAQ;YAACwF,EAAE,EAAE;cAAEoB,OAAO,EAAE;YAAU,CAAE;YAAArB,QAAA,gBACnChE,OAAA,CAAC1B,SAAS;cAAC2F,EAAE,EAAE;gBAAEM,UAAU,EAAE;cAAO,CAAE;cAAAP,QAAA,EAAC;YAAU;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC7D3E,OAAA,CAAC1B,SAAS;cAAC2F,EAAE,EAAE;gBAAEM,UAAU,EAAE;cAAO,CAAE;cAAAP,QAAA,EAAC;YAAK;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACxD3E,OAAA,CAAC1B,SAAS;cAAC2F,EAAE,EAAE;gBAAEM,UAAU,EAAE;cAAO,CAAE;cAAAP,QAAA,EAAC;YAAK;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACxD3E,OAAA,CAAC1B,SAAS;cAAC2F,EAAE,EAAE;gBAAEM,UAAU,EAAE;cAAO,CAAE;cAAAP,QAAA,EAAC;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC1D3E,OAAA,CAAC1B,SAAS;cAAC2F,EAAE,EAAE;gBAAEM,UAAU,EAAE;cAAO,CAAE;cAAAP,QAAA,EAAC;YAAQ;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3D3E,OAAA,CAAC1B,SAAS;cAAC2F,EAAE,EAAE;gBAAEM,UAAU,EAAE;cAAO,CAAE;cAAAP,QAAA,EAAC;YAAM;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACzD3E,OAAA,CAAC1B,SAAS;cAAC2F,EAAE,EAAE;gBAAEM,UAAU,EAAE;cAAO,CAAE;cAAAP,QAAA,EAAC;YAAS;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZ3E,OAAA,CAAC3B,SAAS;UAAA2F,QAAA,EACP5D,OAAO,CAACsC,GAAG,CAAEJ,MAAM,iBAClBtC,OAAA,CAACvB,QAAQ;YAAkBoH,KAAK;YAAA7B,QAAA,gBAC9BhE,OAAA,CAAC1B,SAAS;cAAA0F,QAAA,eACRhE,OAAA,CAACzC,GAAG;gBAAC0G,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAJ,QAAA,gBACjDhE,OAAA,CAACP,YAAY;kBAACwE,EAAE,EAAE;oBAAE6B,EAAE,EAAE,CAAC;oBAAER,KAAK,EAAE;kBAAU;gBAAE;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjD3E,OAAA,CAACxC,UAAU;kBAAC8G,OAAO,EAAC,WAAW;kBAACL,EAAE,EAAE;oBAAEM,UAAU,EAAE;kBAAS,CAAE;kBAAAP,QAAA,EAC1D1B,MAAM,CAACpB;gBAAK;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACZ3E,OAAA,CAAC1B,SAAS;cAAA0F,QAAA,eACRhE,OAAA,CAACxC,UAAU;gBAAC8G,OAAO,EAAC,OAAO;gBAACgB,KAAK,EAAC,eAAe;gBAAAtB,QAAA,EAC9C1B,MAAM,CAACnB,WAAW,CAACoE,MAAM,GAAG,EAAE,GAC3B,GAAGjD,MAAM,CAACnB,WAAW,CAAC4E,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAC3CzD,MAAM,CAACnB;cAAW;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACZ3E,OAAA,CAAC1B,SAAS;cAAA0F,QAAA,eACRhE,OAAA,CAACxC,UAAU;gBAAC8G,OAAO,EAAC,WAAW;gBAACL,EAAE,EAAE;kBAAEM,UAAU,EAAE;gBAAS,CAAE;gBAAAP,QAAA,GAC1D1B,MAAM,CAAClB,KAAK,EAAC,2BAChB;cAAA;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACZ3E,OAAA,CAAC1B,SAAS;cAAA0F,QAAA,eACRhE,OAAA,CAAC7B,IAAI;gBACH6H,KAAK,EAAE1D,MAAM,CAAChB,KAAK,IAAI,OAAQ;gBAC/B2E,IAAI,EAAC,OAAO;gBACZX,KAAK,EAAEhD,MAAM,CAAChB,KAAK,KAAK,OAAO,GAAG,OAAO,GAAGgB,MAAM,CAAChB,KAAK,KAAK,OAAO,GAAG,SAAS,GAAG;cAAU;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZ3E,OAAA,CAAC1B,SAAS;cAAA0F,QAAA,eACRhE,OAAA,CAACzC,GAAG;gBAAC0G,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAJ,QAAA,gBACjDhE,OAAA,CAACN,MAAM;kBAACuE,EAAE,EAAE;oBAAE6B,EAAE,EAAE,CAAC;oBAAER,KAAK,EAAE,SAAS;oBAAEY,QAAQ,EAAE;kBAAG;gBAAE;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACzD3E,OAAA,CAACxC,UAAU;kBAAC8G,OAAO,EAAC,OAAO;kBAAAN,QAAA,EACxBmC,KAAK,CAACC,OAAO,CAAC9D,MAAM,CAACJ,gBAAgB,CAAC,GAAGI,MAAM,CAACJ,gBAAgB,CAACqD,MAAM,GAAG;gBAAC;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACZ3E,OAAA,CAAC1B,SAAS;cAAA0F,QAAA,eACRhE,OAAA,CAACpB,gBAAgB;gBACfyH,OAAO,eACLrG,OAAA,CAACrB,MAAM;kBACL2H,OAAO,EAAEhE,MAAM,CAACjB,QAAS;kBACzBkF,QAAQ,EAAEA,CAAA,KAAMjD,kBAAkB,CAAChB,MAAM,CAACN,GAAG,EAAEM,MAAM,CAACjB,QAAQ,CAAE;kBAChEiE,KAAK,EAAC;gBAAS;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CACF;gBACDqB,KAAK,EAAE1D,MAAM,CAACjB,QAAQ,GAAG,KAAK,GAAG;cAAU;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZ3E,OAAA,CAAC1B,SAAS;cAAA0F,QAAA,eACRhE,OAAA,CAACzC,GAAG;gBAAC0G,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEsC,GAAG,EAAE;gBAAE,CAAE;gBAAAxC,QAAA,gBACnChE,OAAA,CAAC9B,UAAU;kBACT+H,IAAI,EAAC,OAAO;kBACZpB,OAAO,EAAEA,CAAA,KAAMpB,oBAAoB,CAACnB,MAAM,CAAE;kBAC5C2B,EAAE,EAAE;oBAAEqB,KAAK,EAAE;kBAAU,CAAE;kBACzBpE,KAAK,EAAC,qEAAc;kBAAA8C,QAAA,eAEpBhE,OAAA,CAACN,MAAM;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACb3E,OAAA,CAAC9B,UAAU;kBACT+H,IAAI,EAAC,OAAO;kBACZpB,OAAO,EAAEA,CAAA,KAAMxC,gBAAgB,CAACC,MAAM,CAAE;kBACxC2B,EAAE,EAAE;oBAAEqB,KAAK,EAAE;kBAAU,CAAE;kBACzBpE,KAAK,EAAC,gCAAO;kBAAA8C,QAAA,eAEbhE,OAAA,CAACT,IAAI;oBAAAiF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACb3E,OAAA,CAAC9B,UAAU;kBACT+H,IAAI,EAAC,OAAO;kBACZpB,OAAO,EAAEA,CAAA,KAAM7B,YAAY,CAACV,MAAM,CAACN,GAAG,CAAE;kBACxCiC,EAAE,EAAE;oBAAEqB,KAAK,EAAE;kBAAU,CAAE;kBACzBpE,KAAK,EAAC,oBAAK;kBAAA8C,QAAA,eAEXhE,OAAA,CAACR,MAAM;oBAAAgF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA,GA3ECrC,MAAM,CAACN,GAAG;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA4Ef,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAGjB3E,OAAA,CAACnC,MAAM;MAAC4I,IAAI,EAAEjG,UAAW;MAACkG,OAAO,EAAEnE,iBAAkB;MAACoE,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAA5C,QAAA,gBAC3EhE,OAAA,CAAClC,WAAW;QAAAkG,QAAA,EACTlD,aAAa,GAAG,cAAc,GAAG;MAAkB;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC,eACd3E,OAAA,CAACjC,aAAa;QAAAiG,QAAA,eACZhE,OAAA,CAACzC,GAAG;UAAC0G,EAAE,EAAE;YAAE4C,EAAE,EAAE;UAAE,CAAE;UAAA7C,QAAA,gBACjBhE,OAAA,CAAC/B,SAAS;YACR2I,SAAS;YACTZ,KAAK,EAAC,yDAAY;YAClBc,KAAK,EAAE9F,QAAQ,CAACE,KAAM;YACtBqF,QAAQ,EAAGQ,CAAC,IAAK9F,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEE,KAAK,EAAE6F,CAAC,CAACC,MAAM,CAACF;YAAM,CAAC,CAAE;YACrE7C,EAAE,EAAE;cAAEI,EAAE,EAAE;YAAE;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEF3E,OAAA,CAAC/B,SAAS;YACR2I,SAAS;YACTZ,KAAK,EAAC,yDAAY;YAClBiB,SAAS;YACTC,IAAI,EAAE,CAAE;YACRJ,KAAK,EAAE9F,QAAQ,CAACG,WAAY;YAC5BoF,QAAQ,EAAGQ,CAAC,IAAK9F,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEG,WAAW,EAAE4F,CAAC,CAACC,MAAM,CAACF;YAAM,CAAC,CAAE;YAC3E7C,EAAE,EAAE;cAAEI,EAAE,EAAE;YAAE;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEF3E,OAAA,CAAC/B,SAAS;YACR2I,SAAS;YACTZ,KAAK,EAAC,2DAAc;YACpBmB,IAAI,EAAC,QAAQ;YACbL,KAAK,EAAE9F,QAAQ,CAACI,KAAM;YACtBmF,QAAQ,EAAGQ,CAAC,IAAK9F,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEI,KAAK,EAAE2F,CAAC,CAACC,MAAM,CAACF;YAAM,CAAC,CAAE;YACrE7C,EAAE,EAAE;cAAEI,EAAE,EAAE;YAAE;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEF3E,OAAA,CAACzC,GAAG;YAAC0G,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEsC,GAAG,EAAE,CAAC;cAAEnC,EAAE,EAAE;YAAE,CAAE;YAAAL,QAAA,gBAC1ChE,OAAA,CAACjB,WAAW;cAAC6H,SAAS;cAAA5C,QAAA,gBACpBhE,OAAA,CAAChB,UAAU;gBAAAgF,QAAA,EAAC;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChC3E,OAAA,CAACnB,MAAM;gBACLiI,KAAK,EAAE9F,QAAQ,CAACM,KAAM;gBACtB0E,KAAK,EAAC,4CAAS;gBACfO,QAAQ,EAAGQ,CAAC,IAAK9F,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEM,KAAK,EAAEyF,CAAC,CAACC,MAAM,CAACF;gBAAM,CAAC,CAAE;gBAAA9C,QAAA,gBAErEhE,OAAA,CAAClB,QAAQ;kBAACgI,KAAK,EAAC,gCAAO;kBAAA9C,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACxC3E,OAAA,CAAClB,QAAQ;kBAACgI,KAAK,EAAC,gCAAO;kBAAA9C,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACxC3E,OAAA,CAAClB,QAAQ;kBAACgI,KAAK,EAAC,gCAAO;kBAAA9C,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEd3E,OAAA,CAAC/B,SAAS;cACR2I,SAAS;cACTZ,KAAK,EAAC,6FAAuB;cAC7Bc,KAAK,EAAE9F,QAAQ,CAACO,QAAS;cACzBgF,QAAQ,EAAGQ,CAAC,IAAK9F,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEO,QAAQ,EAAEwF,CAAC,CAACC,MAAM,CAACF;cAAM,CAAC;YAAE;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN3E,OAAA,CAAC/B,SAAS;YACR2I,SAAS;YACTZ,KAAK,EAAC,gCAAO;YACbc,KAAK,EAAE9F,QAAQ,CAACQ,QAAS;YACzB+E,QAAQ,EAAGQ,CAAC,IAAK9F,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEQ,QAAQ,EAAEuF,CAAC,CAACC,MAAM,CAACF;YAAM,CAAC,CAAE;YACxE7C,EAAE,EAAE;cAAEI,EAAE,EAAE;YAAE;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEF3E,OAAA,CAACpB,gBAAgB;YACfyH,OAAO,eACLrG,OAAA,CAACrB,MAAM;cACL2H,OAAO,EAAEtF,QAAQ,CAACK,QAAS;cAC3BkF,QAAQ,EAAGQ,CAAC,IAAK9F,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEK,QAAQ,EAAE0F,CAAC,CAACC,MAAM,CAACV;cAAQ,CAAC;YAAE;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CACF;YACDqB,KAAK,EAAC;UAAW;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChB3E,OAAA,CAAChC,aAAa;QAAAgG,QAAA,gBACZhE,OAAA,CAACvC,MAAM;UAACoH,OAAO,EAAEtC,iBAAkB;UAAAyB,QAAA,EAAC;QAAK;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClD3E,OAAA,CAACvC,MAAM;UAACoH,OAAO,EAAErC,YAAa;UAAC8B,OAAO,EAAC,WAAW;UAAAN,QAAA,EAC/ClD,aAAa,GAAG,OAAO,GAAG;QAAO;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT3E,OAAA,CAACnC,MAAM;MACL4I,IAAI,EAAE/F,kBAAmB;MACzBgG,OAAO,EAAEA,CAAA,KAAM/F,qBAAqB,CAAC,KAAK,CAAE;MAC5CgG,QAAQ,EAAC,IAAI;MACbC,SAAS;MAAA5C,QAAA,gBAEThE,OAAA,CAAClC,WAAW;QAAAkG,QAAA,GAAC,wEACI,EAACpD,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEM,KAAK;MAAA;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eACd3E,OAAA,CAACjC,aAAa;QAAAiG,QAAA,gBACZhE,OAAA,CAACxC,UAAU;UAAC8G,OAAO,EAAC,IAAI;UAACL,EAAE,EAAE;YAAEI,EAAE,EAAE;UAAE,CAAE;UAAAL,QAAA,GAAC,yFACrB,EAAC,CAAApD,cAAc,aAAdA,cAAc,wBAAAT,qBAAA,GAAdS,cAAc,CAAEsB,gBAAgB,cAAA/B,qBAAA,uBAAhCA,qBAAA,CAAkCoF,MAAM,KAAI,CAAC,EAAC,GAClE;QAAA;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb3E,OAAA,CAACf,IAAI;UAACgF,EAAE,EAAE;YAAEI,EAAE,EAAE;UAAE,CAAE;UAAAL,QAAA,EACjB1D,QAAQ,CACN+C,MAAM,CAAC+D,OAAO;YAAA,IAAAC,sBAAA;YAAA,OAAIzG,cAAc,aAAdA,cAAc,wBAAAyG,sBAAA,GAAdzG,cAAc,CAAEsB,gBAAgB,cAAAmF,sBAAA,uBAAhCA,sBAAA,CAAkCC,QAAQ,CAACF,OAAO,CAACpF,GAAG,CAAC;UAAA,EAAC,CAC1EU,GAAG,CAAE0E,OAAO,iBACXpH,OAAA,CAACd,QAAQ;YAAA8E,QAAA,gBACPhE,OAAA,CAACb,YAAY;cACXoI,OAAO,EAAEH,OAAO,CAACjF,IAAK;cACtBqF,SAAS,EAAE,eAAeJ,OAAO,CAAChF,WAAW;YAAG;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eACF3E,OAAA,CAACZ,uBAAuB;cAAA4E,QAAA,eACtBhE,OAAA,CAACvC,MAAM;gBACL6G,OAAO,EAAC,UAAU;gBAClBgB,KAAK,EAAC,OAAO;gBACbW,IAAI,EAAC,OAAO;gBACZpB,OAAO,EAAEA,CAAA,KAAMf,qBAAqB,CAACsD,OAAO,CAACpF,GAAG,CAAE;gBAAAgC,QAAA,EACnD;cAED;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACc,CAAC;UAAA,GAdbyC,OAAO,CAACpF,GAAG;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAehB,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEP3E,OAAA,CAACxC,UAAU;UAAC8G,OAAO,EAAC,IAAI;UAACL,EAAE,EAAE;YAAEI,EAAE,EAAE;UAAE,CAAE;UAAAL,QAAA,EAAC;QAExC;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb3E,OAAA,CAACf,IAAI;UAAA+E,QAAA,EACF1D,QAAQ,CACN+C,MAAM,CAAC+D,OAAO;YAAA,IAAAK,sBAAA;YAAA,OAAI,EAAC7G,cAAc,aAAdA,cAAc,gBAAA6G,sBAAA,GAAd7G,cAAc,CAAEsB,gBAAgB,cAAAuF,sBAAA,eAAhCA,sBAAA,CAAkCH,QAAQ,CAACF,OAAO,CAACpF,GAAG,CAAC;UAAA,EAAC,CAC3EU,GAAG,CAAE0E,OAAO,iBACXpH,OAAA,CAACd,QAAQ;YAAA8E,QAAA,gBACPhE,OAAA,CAACb,YAAY;cACXoI,OAAO,EAAEH,OAAO,CAACjF,IAAK;cACtBqF,SAAS,EAAE,eAAeJ,OAAO,CAAChF,WAAW;YAAG;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eACF3E,OAAA,CAACZ,uBAAuB;cAAA4E,QAAA,eACtBhE,OAAA,CAACvC,MAAM;gBACL6G,OAAO,EAAC,WAAW;gBACnBgB,KAAK,EAAC,SAAS;gBACfW,IAAI,EAAC,OAAO;gBACZpB,OAAO,EAAEA,CAAA,KAAMnB,mBAAmB,CAAC0D,OAAO,CAACpF,GAAG,CAAE;gBAAAgC,QAAA,EACjD;cAED;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACc,CAAC;UAAA,GAdbyC,OAAO,CAACpF,GAAG;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAehB,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChB3E,OAAA,CAAChC,aAAa;QAAAgG,QAAA,eACZhE,OAAA,CAACvC,MAAM;UAACoH,OAAO,EAAEA,CAAA,KAAMlE,qBAAqB,CAAC,KAAK,CAAE;UAAAqD,QAAA,EAAC;QAAK;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACzE,EAAA,CApiBID,gBAAgB;AAAAyH,EAAA,GAAhBzH,gBAAgB;AAsiBtB,eAAeA,gBAAgB;AAAC,IAAAyH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
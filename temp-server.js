const express = require('express');
const cors = require('cors');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

const app = express();
const PORT = process.env.PORT || 5000;
const JWT_SECRET = 'your-secret-key';

// Middleware
app.use(cors());
app.use(express.json());

// بيانات مؤقتة في الذاكرة
let users = [
  {
    _id: '1',
    email: '<EMAIL>',
    password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
    role: 'admin',
    name: 'المدير العام',
    isActive: true
  }
];

let students = [
  {
    _id: '2',
    studentCode: '123456',
    name: 'أحمد محمد',
    isActive: true,
    enrolledCourses: ['1', '2'],
    progress: {
      '1': { completedVideos: 8, totalVideos: 12, progress: 67 },
      '2': { completedVideos: 3, totalVideos: 10, progress: 30 }
    },
    joinDate: '2024-01-15',
    lastActivity: new Date().toISOString()
  },
  {
    _id: '3',
    studentCode: '789012',
    name: 'فاطمة علي',
    isActive: true,
    enrolledCourses: ['1'],
    progress: {
      '1': { completedVideos: 12, totalVideos: 12, progress: 100 }
    },
    joinDate: '2024-01-10',
    lastActivity: new Date().toISOString()
  }
];

let courses = [
  {
    _id: '1',
    title: 'أساسيات التسويق الرقمي',
    description: 'تعلم أساسيات التسويق الرقمي من الصفر حتى الاحتراف',
    price: 299,
    isActive: true,
    instructor: 'علاء عبد الحميد',
    rating: 4.8,
    totalVideos: 12,
    duration: '8 ساعات',
    level: 'مبتدئ',
    category: 'التسويق الرقمي',
    thumbnail: '/api/placeholder/300/200',
    videos: [
      { id: 1, title: 'مقدمة في التسويق الرقمي', duration: '15:30', isCompleted: false },
      { id: 2, title: 'أساسيات SEO', duration: '22:45', isCompleted: false },
      { id: 3, title: 'التسويق عبر وسائل التواصل', duration: '18:20', isCompleted: false },
      { id: 4, title: 'إعلانات جوجل', duration: '25:15', isCompleted: false },
      { id: 5, title: 'التسويق بالمحتوى', duration: '20:30', isCompleted: false }
    ],
    enrolledStudents: ['2', '3']
  },
  {
    _id: '2',
    title: 'إدارة وسائل التواصل الاجتماعي',
    description: 'كيفية إدارة حسابات التواصل الاجتماعي بفعالية وزيادة المتابعين',
    price: 399,
    isActive: true,
    instructor: 'علاء عبد الحميد',
    rating: 4.9,
    totalVideos: 10,
    duration: '6 ساعات',
    level: 'متوسط',
    category: 'وسائل التواصل',
    thumbnail: '/api/placeholder/300/200',
    videos: [
      { id: 1, title: 'استراتيجية المحتوى', duration: '25:10', isCompleted: false },
      { id: 2, title: 'أدوات الجدولة', duration: '20:30', isCompleted: false },
      { id: 3, title: 'تحليل الأداء', duration: '18:45', isCompleted: false }
    ],
    enrolledStudents: ['2']
  },
  {
    _id: '3',
    title: 'التسويق عبر البريد الإلكتروني',
    description: 'استراتيجيات فعالة للتسويق عبر البريد الإلكتروني وزيادة المبيعات',
    price: 249,
    isActive: true,
    instructor: 'علاء عبد الحميد',
    rating: 4.7,
    totalVideos: 8,
    duration: '5 ساعات',
    level: 'مبتدئ',
    category: 'التسويق الإلكتروني',
    thumbnail: '/api/placeholder/300/200',
    videos: [
      { id: 1, title: 'بناء قائمة بريدية', duration: '30:00', isCompleted: false },
      { id: 2, title: 'تصميم الرسائل', duration: '25:45', isCompleted: false }
    ],
    enrolledStudents: []
  }
];

// Routes
app.get('/api/test', (req, res) => {
  res.json({ message: 'الخادم يعمل بشكل صحيح!' });
});

// التحقق من المصادقة
app.get('/api/auth/me', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (!token) {
    return res.status(401).json({ message: 'لا يوجد رمز مصادقة' });
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    const user = users.find(u => u._id === decoded.userId) ||
                 students.find(s => s._id === decoded.userId);

    if (!user) {
      return res.status(401).json({ message: 'المستخدم غير موجود' });
    }

    res.json({ user: {
      id: user._id,
      email: user.email || undefined,
      studentCode: user.studentCode || undefined,
      name: user.name,
      role: decoded.role
    }});
  } catch (error) {
    res.status(401).json({ message: 'رمز المصادقة غير صالح' });
  }
});

// تسجيل دخول المدير
app.post('/api/auth/admin/login', async (req, res) => {
  try {
    console.log('🔐 طلب تسجيل دخول مدير:', req.body);
    const { email, password } = req.body;
    
    const admin = users.find(u => u.email === email && u.role === 'admin');
    if (!admin) {
      return res.status(401).json({ message: 'بيانات تسجيل الدخول غير صحيحة' });
    }

    // للاختبار، سنقبل كلمة المرور "Admin123!"
    if (password !== 'Admin123!') {
      return res.status(401).json({ message: 'بيانات تسجيل الدخول غير صحيحة' });
    }

    const token = jwt.sign(
      { userId: admin._id, role: admin.role },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    res.json({
      token,
      user: {
        id: admin._id,
        email: admin.email,
        name: admin.name,
        role: admin.role
      }
    });
  } catch (error) {
    console.error('خطأ في تسجيل دخول المدير:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// تسجيل دخول الطالب
app.post('/api/auth/student/login', async (req, res) => {
  try {
    console.log('🎓 طلب تسجيل دخول طالب:', req.body);
    const { code } = req.body;
    const studentCode = code;
    
    const student = students.find(s => s.studentCode === studentCode);
    if (!student) {
      return res.status(401).json({ message: 'كود الطالب غير صحيح' });
    }

    const token = jwt.sign(
      { userId: student._id, role: 'student' },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    res.json({
      token,
      user: {
        id: student._id,
        studentCode: student.studentCode,
        name: student.name,
        role: 'student'
      }
    });
  } catch (error) {
    console.error('خطأ في تسجيل دخول الطالب:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// الحصول على الإحصائيات
app.get('/api/admin/stats', (req, res) => {
  res.json({
    totalStudents: students.length,
    totalCourses: courses.length,
    totalVideos: courses.reduce((total, course) => total + course.videos.length, 0),
    totalCertificates: 0
  });
});

// الحصول على الطلاب
app.get('/api/admin/students', (req, res) => {
  res.json(students);
});

// الحصول على الدورات
app.get('/api/admin/courses', (req, res) => {
  res.json(courses);
});

// إضافة طالب جديد
app.post('/api/admin/students', (req, res) => {
  const { name } = req.body;
  const studentCode = Math.floor(100000 + Math.random() * 900000).toString();
  
  const newStudent = {
    _id: (students.length + 1).toString(),
    studentCode,
    name,
    isActive: true,
    courses: []
  };
  
  students.push(newStudent);
  res.json(newStudent);
});

// الحصول على جميع الكورسات (للمدير)
app.get('/api/admin/courses', (req, res) => {
  console.log('📚 طلب جلب الكورسات من المدير');
  res.json(courses);
});

// إضافة دورة جديدة
app.post('/api/admin/courses', (req, res) => {
  const { title, description, price, level, duration, category } = req.body;

  const newCourse = {
    _id: (courses.length + 1).toString(),
    title,
    description,
    price: parseInt(price),
    level: level || 'مبتدئ',
    duration: duration || '',
    category: category || '',
    instructor: 'علاء عبد الحميد',
    rating: 4.5,
    totalVideos: 0,
    isActive: true,
    videos: [],
    enrolledStudents: []
  };

  courses.push(newCourse);
  res.json(newCourse);
});

// الحصول على دورات الطالب
app.get('/api/student/courses', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (!token) {
    return res.status(401).json({ message: 'لا يوجد رمز مصادقة' });
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    const student = students.find(s => s._id === decoded.userId);

    if (!student) {
      return res.status(404).json({ message: 'الطالب غير موجود' });
    }

    const studentCourses = student.enrolledCourses.map(courseId => {
      const course = courses.find(c => c._id === courseId);
      if (!course) return null;

      const progress = student.progress[courseId] || { completedVideos: 0, totalVideos: course.totalVideos, progress: 0 };

      return {
        _id: course._id,
        title: course.title,
        description: course.description,
        instructor: course.instructor,
        rating: course.rating,
        level: course.level,
        duration: course.duration,
        category: course.category,
        totalVideos: course.totalVideos,
        completedVideos: progress.completedVideos,
        progress: progress.progress,
        isEnrolled: true,
        videos: course.videos
      };
    }).filter(course => course !== null);

    res.json(studentCourses);
  } catch (error) {
    res.status(401).json({ message: 'رمز المصادقة غير صالح' });
  }
});

// الحصول على إحصائيات الطالب
app.get('/api/student/stats', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (!token) {
    return res.status(401).json({ message: 'لا يوجد رمز مصادقة' });
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    const student = students.find(s => s._id === decoded.userId);

    if (!student) {
      return res.status(404).json({ message: 'الطالب غير موجود' });
    }

    let totalVideos = 0;
    let completedVideos = 0;
    let certificates = 0;

    student.enrolledCourses.forEach(courseId => {
      const course = courses.find(c => c._id === courseId);
      if (course) {
        totalVideos += course.totalVideos;
        const progress = student.progress[courseId];
        if (progress) {
          completedVideos += progress.completedVideos;
          if (progress.progress === 100) {
            certificates++;
          }
        }
      }
    });

    const overallProgress = totalVideos > 0 ? Math.round((completedVideos / totalVideos) * 100) : 0;

    res.json({
      enrolledCourses: student.enrolledCourses.length,
      completedVideos,
      totalVideos,
      certificates,
      overallProgress
    });
  } catch (error) {
    res.status(401).json({ message: 'رمز المصادقة غير صالح' });
  }
});

// الحصول على جميع الطلاب (للمدير)
app.get('/api/admin/students', (req, res) => {
  console.log('👥 طلب جلب الطلاب من المدير');
  const studentsWithCourseNames = students.map(student => ({
    ...student,
    enrolledCoursesNames: student.enrolledCourses.map(courseId => {
      const course = courses.find(c => c._id === courseId);
      return course ? course.title : 'كورس محذوف';
    })
  }));
  res.json(studentsWithCourseNames);
});

// إضافة طالب جديد
app.post('/api/admin/students', (req, res) => {
  const { name, isActive = true } = req.body;

  const newStudent = {
    _id: (students.length + 2).toString(),
    studentCode: Math.floor(100000 + Math.random() * 900000).toString(),
    name,
    isActive,
    enrolledCourses: [],
    progress: {},
    joinDate: new Date().toISOString().split('T')[0],
    lastActivity: new Date().toISOString()
  };

  students.push(newStudent);
  res.json(newStudent);
});

// تحديث طالب
app.put('/api/admin/students/:id', (req, res) => {
  const { id } = req.params;
  const { name, isActive } = req.body;

  const studentIndex = students.findIndex(s => s._id === id);
  if (studentIndex === -1) {
    return res.status(404).json({ message: 'الطالب غير موجود' });
  }

  students[studentIndex] = {
    ...students[studentIndex],
    name: name || students[studentIndex].name,
    isActive: isActive !== undefined ? isActive : students[studentIndex].isActive
  };

  res.json(students[studentIndex]);
});

// حذف طالب
app.delete('/api/admin/students/:id', (req, res) => {
  const { id } = req.params;
  const studentIndex = students.findIndex(s => s._id === id);

  if (studentIndex === -1) {
    return res.status(404).json({ message: 'الطالب غير موجود' });
  }

  students.splice(studentIndex, 1);
  res.json({ message: 'تم حذف الطالب بنجاح' });
});

// تسجيل طالب في كورس
app.post('/api/admin/students/:studentId/enroll/:courseId', (req, res) => {
  const { studentId, courseId } = req.params;

  const student = students.find(s => s._id === studentId);
  const course = courses.find(c => c._id === courseId);

  if (!student) {
    return res.status(404).json({ message: 'الطالب غير موجود' });
  }

  if (!course) {
    return res.status(404).json({ message: 'الكورس غير موجود' });
  }

  if (!student.enrolledCourses.includes(courseId)) {
    student.enrolledCourses.push(courseId);
    student.progress[courseId] = {
      completedVideos: 0,
      totalVideos: course.totalVideos,
      progress: 0
    };

    if (!course.enrolledStudents.includes(studentId)) {
      course.enrolledStudents.push(studentId);
    }
  }

  res.json({ message: 'تم تسجيل الطالب في الكورس بنجاح' });
});

// إلغاء تسجيل طالب من كورس
app.delete('/api/admin/students/:studentId/unenroll/:courseId', (req, res) => {
  const { studentId, courseId } = req.params;

  const student = students.find(s => s._id === studentId);
  const course = courses.find(c => c._id === courseId);

  if (!student) {
    return res.status(404).json({ message: 'الطالب غير موجود' });
  }

  student.enrolledCourses = student.enrolledCourses.filter(id => id !== courseId);
  delete student.progress[courseId];

  if (course) {
    course.enrolledStudents = course.enrolledStudents.filter(id => id !== studentId);
  }

  res.json({ message: 'تم إلغاء تسجيل الطالب من الكورس بنجاح' });
});

// إرسال رسالة للمدير
app.post('/api/student/contact-admin', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (!token) {
    return res.status(401).json({ message: 'لا يوجد رمز مصادقة' });
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    const student = students.find(s => s._id === decoded.userId);

    if (!student) {
      return res.status(404).json({ message: 'الطالب غير موجود' });
    }

    const { subject, message, priority, contactMethod } = req.body;

    // في التطبيق الحقيقي، ستحفظ الرسالة في قاعدة البيانات وترسل إشعار للمدير
    const contactMessage = {
      id: Date.now().toString(),
      from: {
        studentId: student._id,
        studentCode: student.studentCode,
        name: student.name
      },
      subject,
      message,
      priority,
      contactMethod,
      timestamp: new Date().toISOString(),
      status: 'جديد'
    };

    console.log('📧 رسالة جديدة من الطالب:', contactMessage);

    res.json({
      message: 'تم إرسال رسالتك بنجاح! سيتم الرد عليك في أقرب وقت ممكن.',
      messageId: contactMessage.id
    });
  } catch (error) {
    res.status(401).json({ message: 'رمز المصادقة غير صالح' });
  }
});

// الحصول على إحصائيات لوحة التحكم (للمدير)
app.get('/api/admin/dashboard-stats', (req, res) => {
  console.log('📊 طلب إحصائيات لوحة التحكم من المدير');

  const totalStudents = students.length;
  const activeStudents = students.filter(s => s.isActive).length;
  const totalCourses = courses.length;
  const activeCourses = courses.filter(c => c.isActive).length;
  const totalVideos = courses.reduce((total, course) => total + (course.videos?.length || 0), 0);
  const totalEnrollments = students.reduce((total, student) => total + (student.enrolledCourses?.length || 0), 0);

  res.json({
    totalStudents,
    activeStudents,
    totalCourses,
    activeCourses,
    totalVideos,
    totalCertificates: totalEnrollments,
    recentActivity: [
      {
        id: 1,
        type: 'student',
        title: 'طالب جديد انضم',
        description: 'أحمد محمد انضم إلى المنصة',
        time: 'منذ 5 دقائق'
      },
      {
        id: 2,
        type: 'course',
        title: 'تم تسجيل طالب في كورس',
        description: 'فاطمة علي سجلت في أساسيات التسويق الرقمي',
        time: 'منذ ساعة'
      }
    ]
  });
});

app.listen(PORT, () => {
  console.log(`🚀 الخادم المؤقت يعمل على المنفذ ${PORT}`);
  console.log(`🌐 الموقع متاح على: http://localhost:${PORT}`);
  console.log(`📱 API متاح على: http://localhost:${PORT}/api/test`);
  console.log(`✅ الخادم يعمل بدون قاعدة بيانات للاختبار`);
  console.log(`👨‍💼 بيانات المدير: <EMAIL> / Admin123!`);
  console.log(`👨‍🎓 كود طالب تجريبي: 123456`);
});

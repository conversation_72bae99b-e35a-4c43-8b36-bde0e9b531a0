{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0643\\u0648\\u0633\\u0627\\u062A\\\\frontend\\\\src\\\\components\\\\admin\\\\CertificateManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Button, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Chip, IconButton, Tooltip, Alert, Grid, Card, CardContent, Avatar, Divider } from '@mui/material';\nimport { Add, Upload, Download, Visibility, Delete, WorkspacePremium, School, Person, CalendarToday } from '@mui/icons-material';\nimport { toast } from 'react-toastify';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CertificateManagement = () => {\n  _s();\n  const [certificates, setCertificates] = useState([]);\n  const [students, setStudents] = useState([]);\n  const [courses, setCourses] = useState([]);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [formData, setFormData] = useState({\n    studentId: '',\n    courseId: '',\n    certificateName: '',\n    description: ''\n  });\n  useEffect(() => {\n    fetchData();\n  }, []);\n  const fetchData = async () => {\n    try {\n      // جلب الطلاب والكورسات\n      const [studentsRes, coursesRes] = await Promise.all([axios.get('/admin/students'), axios.get('/admin/courses')]);\n      setStudents(studentsRes.data);\n      setCourses(coursesRes.data);\n\n      // بيانات تجريبية للشهادات\n      setCertificates([{\n        id: '1',\n        studentId: 'student1',\n        studentName: 'أحمد محمد',\n        courseId: 'course1',\n        courseName: 'أساسيات التسويق الرقمي',\n        certificateName: 'شهادة إتمام أساسيات التسويق الرقمي',\n        description: 'تم منح هذه الشهادة للطالب أحمد محمد لإتمام دورة أساسيات التسويق الرقمي بنجاح',\n        issuedDate: '2024-01-20',\n        issuedBy: 'علاء عبد الحميد',\n        fileUrl: null\n      }]);\n    } catch (error) {\n      console.error('خطأ في جلب البيانات:', error);\n      // بيانات احتياطية\n      setStudents([{\n        _id: 'student1',\n        name: 'أحمد محمد',\n        studentCode: '123456'\n      }]);\n      setCourses([{\n        _id: 'course1',\n        title: 'أساسيات التسويق الرقمي'\n      }, {\n        _id: 'course2',\n        title: 'إدارة وسائل التواصل الاجتماعي'\n      }]);\n    }\n  };\n  const handleOpenDialog = () => {\n    setFormData({\n      studentId: '',\n      courseId: '',\n      certificateName: '',\n      description: ''\n    });\n    setSelectedFile(null);\n    setOpenDialog(true);\n  };\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setFormData({\n      studentId: '',\n      courseId: '',\n      certificateName: '',\n      description: ''\n    });\n    setSelectedFile(null);\n  };\n  const handleFileChange = event => {\n    const file = event.target.files[0];\n    if (file) {\n      // التحقق من نوع الملف\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];\n      if (!allowedTypes.includes(file.type)) {\n        toast.error('يُسمح فقط بملفات الصور (JPG, PNG) أو PDF');\n        return;\n      }\n\n      // التحقق من حجم الملف (5MB)\n      if (file.size > 5 * 1024 * 1024) {\n        toast.error('حجم الملف يجب أن يكون أقل من 5 ميجابايت');\n        return;\n      }\n      setSelectedFile(file);\n    }\n  };\n  const handleSubmit = async () => {\n    if (!formData.studentId || !formData.courseId) {\n      toast.error('يرجى اختيار الطالب والكورس');\n      return;\n    }\n    try {\n      const student = students.find(s => s._id === formData.studentId);\n      const course = courses.find(c => c._id === formData.courseId);\n      if (!student || !course) {\n        toast.error('الطالب أو الكورس غير موجود');\n        return;\n      }\n\n      // إنشاء FormData لرفع الملف\n      const submitData = new FormData();\n      submitData.append('studentId', formData.studentId);\n      submitData.append('courseId', formData.courseId);\n      submitData.append('certificateName', formData.certificateName || `شهادة إتمام ${course.title}`);\n      submitData.append('description', formData.description || `تم منح هذه الشهادة للطالب ${student.name} لإتمام دورة ${course.title} بنجاح`);\n      if (selectedFile) {\n        submitData.append('certificate', selectedFile);\n      }\n\n      // محاولة إرسال للخادم\n      try {\n        const response = await axios.post(`/admin/students/${formData.studentId}/certificate`, submitData, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n        toast.success('تم رفع الشهادة بنجاح!');\n      } catch (error) {\n        console.error('خطأ في رفع الشهادة:', error);\n        // محاكاة النجاح للاختبار\n        toast.success('تم رفع الشهادة بنجاح! (وضع الاختبار)');\n      }\n\n      // إضافة الشهادة للقائمة المحلية\n      const newCertificate = {\n        id: Date.now().toString(),\n        studentId: formData.studentId,\n        studentName: student.name,\n        courseId: formData.courseId,\n        courseName: course.title,\n        certificateName: formData.certificateName || `شهادة إتمام ${course.title}`,\n        description: formData.description || `تم منح هذه الشهادة للطالب ${student.name} لإتمام دورة ${course.title} بنجاح`,\n        issuedDate: new Date().toISOString().split('T')[0],\n        issuedBy: 'علاء عبد الحميد',\n        fileUrl: selectedFile ? URL.createObjectURL(selectedFile) : null\n      };\n      setCertificates([...certificates, newCertificate]);\n      handleCloseDialog();\n    } catch (error) {\n      console.error('خطأ في إنشاء الشهادة:', error);\n      toast.error('حدث خطأ في إنشاء الشهادة');\n    }\n  };\n  const handleDelete = async certificateId => {\n    if (window.confirm('هل أنت متأكد من حذف هذه الشهادة؟')) {\n      setCertificates(certificates.filter(cert => cert.id !== certificateId));\n      toast.success('تم حذف الشهادة بنجاح');\n    }\n  };\n  const handleDownload = certificate => {\n    if (certificate.fileUrl) {\n      // تحميل الملف\n      const link = document.createElement('a');\n      link.href = certificate.fileUrl;\n      link.download = `${certificate.certificateName}.pdf`;\n      link.click();\n    } else {\n      toast.info('لا يوجد ملف مرفق مع هذه الشهادة');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          fontWeight: 'bold',\n          color: '#1976d2'\n        },\n        children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0627\\u062A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(Add, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 22\n        }, this),\n        onClick: handleOpenDialog,\n        sx: {\n          borderRadius: 2\n        },\n        children: \"\\u0631\\u0641\\u0639 \\u0634\\u0647\\u0627\\u062F\\u0629 \\u062C\\u062F\\u064A\\u062F\\u0629\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            bgcolor: '#e3f2fd',\n            borderLeft: '4px solid #1976d2'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  sx: {\n                    fontWeight: 'bold',\n                    color: '#1976d2'\n                  },\n                  children: certificates.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"textSecondary\",\n                  children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0627\\u062A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(WorkspacePremium, {\n                sx: {\n                  fontSize: 40,\n                  color: '#1976d2'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            bgcolor: '#e8f5e8',\n            borderLeft: '4px solid #4caf50'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  sx: {\n                    fontWeight: 'bold',\n                    color: '#4caf50'\n                  },\n                  children: certificates.filter(c => c.fileUrl).length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"textSecondary\",\n                  children: \"\\u0634\\u0647\\u0627\\u062F\\u0627\\u062A \\u0645\\u0631\\u0641\\u0648\\u0639\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Upload, {\n                sx: {\n                  fontSize: 40,\n                  color: '#4caf50'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            bgcolor: '#fff3e0',\n            borderLeft: '4px solid #ff9800'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  sx: {\n                    fontWeight: 'bold',\n                    color: '#ff9800'\n                  },\n                  children: new Set(certificates.map(c => c.studentId)).size\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"textSecondary\",\n                  children: \"\\u0637\\u0644\\u0627\\u0628 \\u062D\\u0627\\u0635\\u0644\\u064A\\u0646 \\u0639\\u0644\\u0649 \\u0634\\u0647\\u0627\\u062F\\u0627\\u062A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Person, {\n                sx: {\n                  fontSize: 40,\n                  color: '#ff9800'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      sx: {\n        borderRadius: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            sx: {\n              bgcolor: '#f5f5f5'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold'\n              },\n              children: \"\\u0627\\u0644\\u0637\\u0627\\u0644\\u0628\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold'\n              },\n              children: \"\\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold'\n              },\n              children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold'\n              },\n              children: \"\\u062A\\u0627\\u0631\\u064A\\u062E \\u0627\\u0644\\u0625\\u0635\\u062F\\u0627\\u0631\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold'\n              },\n              children: \"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold'\n              },\n              children: \"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: certificates.map(certificate => /*#__PURE__*/_jsxDEV(TableRow, {\n            hover: true,\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                  sx: {\n                    mr: 2,\n                    bgcolor: '#1976d2'\n                  },\n                  children: certificate.studentName.charAt(0)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 500\n                  },\n                  children: certificate.studentName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: certificate.courseName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontWeight: 500\n                },\n                children: certificate.certificateName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(CalendarToday, {\n                  sx: {\n                    fontSize: 16,\n                    mr: 1,\n                    color: 'text.secondary'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: certificate.issuedDate\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: certificate.fileUrl ? 'مرفوعة' : 'نصية فقط',\n                color: certificate.fileUrl ? 'success' : 'default',\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  gap: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u062A\\u0641\\u0627\\u0635\\u064A\\u0644\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    sx: {\n                      color: '#1976d2'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 356,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 355,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 21\n                }, this), certificate.fileUrl && /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"\\u062A\\u062D\\u0645\\u064A\\u0644 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0629\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => handleDownload(certificate),\n                    sx: {\n                      color: '#4caf50'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Download, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 367,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"\\u062D\\u0630\\u0641 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0629\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => handleDelete(certificate.id),\n                    sx: {\n                      color: '#d32f2f'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Delete, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 378,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 17\n            }, this)]\n          }, certificate.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 302,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: handleCloseDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(WorkspacePremium, {\n            sx: {\n              color: '#1976d2'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 13\n          }, this), \"\\u0631\\u0641\\u0639 \\u0634\\u0647\\u0627\\u062F\\u0629 \\u062C\\u062F\\u064A\\u062F\\u0629\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0637\\u0627\\u0644\\u0628\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.studentId,\n                  label: \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0637\\u0627\\u0644\\u0628\",\n                  onChange: e => setFormData({\n                    ...formData,\n                    studentId: e.target.value\n                  }),\n                  children: students.map(student => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: student._id,\n                    children: [student.name, \" (\", student.studentCode, \")\"]\n                  }, student._id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 409,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.courseId,\n                  label: \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\",\n                  onChange: e => setFormData({\n                    ...formData,\n                    courseId: e.target.value\n                  }),\n                  children: courses.map(course => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: course._id,\n                    children: course.title\n                  }, course._id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 426,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0629 (\\u0627\\u062E\\u062A\\u064A\\u0627\\u0631\\u064A)\",\n                value: formData.certificateName,\n                onChange: e => setFormData({\n                  ...formData,\n                  certificateName: e.target.value\n                }),\n                placeholder: \"\\u0633\\u064A\\u062A\\u0645 \\u0625\\u0646\\u0634\\u0627\\u0621 \\u0627\\u0633\\u0645 \\u062A\\u0644\\u0642\\u0627\\u0626\\u064A \\u0625\\u0630\\u0627 \\u062A\\u064F\\u0631\\u0643 \\u0641\\u0627\\u0631\\u063A\\u0627\\u064B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                multiline: true,\n                rows: 3,\n                label: \"\\u0648\\u0635\\u0641 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0629 (\\u0627\\u062E\\u062A\\u064A\\u0627\\u0631\\u064A)\",\n                value: formData.description,\n                onChange: e => setFormData({\n                  ...formData,\n                  description: e.target.value\n                }),\n                placeholder: \"\\u0633\\u064A\\u062A\\u0645 \\u0625\\u0646\\u0634\\u0627\\u0621 \\u0648\\u0635\\u0641 \\u062A\\u0644\\u0642\\u0627\\u0626\\u064A \\u0625\\u0630\\u0627 \\u062A\\u064F\\u0631\\u0643 \\u0641\\u0627\\u0631\\u063A\\u0627\\u064B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: [/*#__PURE__*/_jsxDEV(Divider, {\n                sx: {\n                  my: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  mb: 2\n                },\n                children: \"\\u0631\\u0641\\u0639 \\u0645\\u0644\\u0641 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0629 (\\u0627\\u062E\\u062A\\u064A\\u0627\\u0631\\u064A)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                accept: \"image/*,.pdf\",\n                style: {\n                  display: 'none'\n                },\n                id: \"certificate-file\",\n                type: \"file\",\n                onChange: handleFileChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"certificate-file\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  component: \"span\",\n                  startIcon: /*#__PURE__*/_jsxDEV(Upload, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 473,\n                    columnNumber: 32\n                  }, this),\n                  sx: {\n                    mb: 2\n                  },\n                  children: \"\\u0627\\u062E\\u062A\\u0631 \\u0645\\u0644\\u0641 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 469,\n                columnNumber: 17\n              }, this), selectedFile && /*#__PURE__*/_jsxDEV(Alert, {\n                severity: \"success\",\n                sx: {\n                  mt: 2\n                },\n                children: [\"\\u062A\\u0645 \\u0627\\u062E\\u062A\\u064A\\u0627\\u0631 \\u0627\\u0644\\u0645\\u0644\\u0641: \", selectedFile.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"textSecondary\",\n                sx: {\n                  mt: 1\n                },\n                children: \"\\u064A\\u064F\\u0633\\u0645\\u062D \\u0628\\u0645\\u0644\\u0641\\u0627\\u062A \\u0627\\u0644\\u0635\\u0648\\u0631 (JPG, PNG) \\u0623\\u0648 PDF \\u0628\\u062D\\u062F \\u0623\\u0642\\u0635\\u0649 5 \\u0645\\u064A\\u062C\\u0627\\u0628\\u0627\\u064A\\u062A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDialog,\n          children: \"\\u0625\\u0644\\u063A\\u0627\\u0621\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 494,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmit,\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(Upload, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 73\n          }, this),\n          children: \"\\u0631\\u0641\\u0639 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 493,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 390,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 228,\n    columnNumber: 5\n  }, this);\n};\n_s(CertificateManagement, \"1OZvIu1LLzsSnbpDu0JiEsXgkVY=\");\n_c = CertificateManagement;\nexport default CertificateManagement;\nvar _c;\n$RefreshReg$(_c, \"CertificateManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Chip", "IconButton", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Avatar", "Divider", "Add", "Upload", "Download", "Visibility", "Delete", "WorkspacePremium", "School", "Person", "CalendarToday", "toast", "axios", "jsxDEV", "_jsxDEV", "CertificateManagement", "_s", "certificates", "setCertificates", "students", "setStudents", "courses", "setCourses", "openDialog", "setOpenDialog", "selectedFile", "setSelectedFile", "formData", "setFormData", "studentId", "courseId", "certificateName", "description", "fetchData", "studentsRes", "coursesRes", "Promise", "all", "get", "data", "id", "studentName", "courseName", "issuedDate", "issuedBy", "fileUrl", "error", "console", "_id", "name", "studentCode", "title", "handleOpenDialog", "handleCloseDialog", "handleFileChange", "event", "file", "target", "files", "allowedTypes", "includes", "type", "size", "handleSubmit", "student", "find", "s", "course", "c", "submitData", "FormData", "append", "response", "post", "headers", "success", "newCertificate", "Date", "now", "toString", "toISOString", "split", "URL", "createObjectURL", "handleDelete", "certificateId", "window", "confirm", "filter", "cert", "handleDownload", "certificate", "link", "document", "createElement", "href", "download", "click", "info", "children", "sx", "display", "justifyContent", "alignItems", "mb", "variant", "fontWeight", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "startIcon", "onClick", "borderRadius", "container", "spacing", "item", "xs", "md", "bgcolor", "borderLeft", "length", "fontSize", "Set", "map", "component", "hover", "mr", "char<PERSON>t", "label", "gap", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "pt", "value", "onChange", "e", "placeholder", "multiline", "rows", "my", "accept", "style", "htmlFor", "severity", "mt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/كوسات/frontend/src/components/admin/CertificateManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Chip,\n  IconButton,\n  Tooltip,\n  Alert,\n  Grid,\n  Card,\n  CardContent,\n  Avatar,\n  Divider\n} from '@mui/material';\nimport {\n  Add,\n  Upload,\n  Download,\n  Visibility,\n  Delete,\n  WorkspacePremium,\n  School,\n  Person,\n  CalendarToday\n} from '@mui/icons-material';\nimport { toast } from 'react-toastify';\nimport axios from 'axios';\n\nconst CertificateManagement = () => {\n  const [certificates, setCertificates] = useState([]);\n  const [students, setStudents] = useState([]);\n  const [courses, setCourses] = useState([]);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [formData, setFormData] = useState({\n    studentId: '',\n    courseId: '',\n    certificateName: '',\n    description: ''\n  });\n\n  useEffect(() => {\n    fetchData();\n  }, []);\n\n  const fetchData = async () => {\n    try {\n      // جلب الطلاب والكورسات\n      const [studentsRes, coursesRes] = await Promise.all([\n        axios.get('/admin/students'),\n        axios.get('/admin/courses')\n      ]);\n      \n      setStudents(studentsRes.data);\n      setCourses(coursesRes.data);\n      \n      // بيانات تجريبية للشهادات\n      setCertificates([\n        {\n          id: '1',\n          studentId: 'student1',\n          studentName: 'أحمد محمد',\n          courseId: 'course1',\n          courseName: 'أساسيات التسويق الرقمي',\n          certificateName: 'شهادة إتمام أساسيات التسويق الرقمي',\n          description: 'تم منح هذه الشهادة للطالب أحمد محمد لإتمام دورة أساسيات التسويق الرقمي بنجاح',\n          issuedDate: '2024-01-20',\n          issuedBy: 'علاء عبد الحميد',\n          fileUrl: null\n        }\n      ]);\n    } catch (error) {\n      console.error('خطأ في جلب البيانات:', error);\n      // بيانات احتياطية\n      setStudents([\n        { _id: 'student1', name: 'أحمد محمد', studentCode: '123456' }\n      ]);\n      setCourses([\n        { _id: 'course1', title: 'أساسيات التسويق الرقمي' },\n        { _id: 'course2', title: 'إدارة وسائل التواصل الاجتماعي' }\n      ]);\n    }\n  };\n\n  const handleOpenDialog = () => {\n    setFormData({\n      studentId: '',\n      courseId: '',\n      certificateName: '',\n      description: ''\n    });\n    setSelectedFile(null);\n    setOpenDialog(true);\n  };\n\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setFormData({\n      studentId: '',\n      courseId: '',\n      certificateName: '',\n      description: ''\n    });\n    setSelectedFile(null);\n  };\n\n  const handleFileChange = (event) => {\n    const file = event.target.files[0];\n    if (file) {\n      // التحقق من نوع الملف\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];\n      if (!allowedTypes.includes(file.type)) {\n        toast.error('يُسمح فقط بملفات الصور (JPG, PNG) أو PDF');\n        return;\n      }\n      \n      // التحقق من حجم الملف (5MB)\n      if (file.size > 5 * 1024 * 1024) {\n        toast.error('حجم الملف يجب أن يكون أقل من 5 ميجابايت');\n        return;\n      }\n      \n      setSelectedFile(file);\n    }\n  };\n\n  const handleSubmit = async () => {\n    if (!formData.studentId || !formData.courseId) {\n      toast.error('يرجى اختيار الطالب والكورس');\n      return;\n    }\n\n    try {\n      const student = students.find(s => s._id === formData.studentId);\n      const course = courses.find(c => c._id === formData.courseId);\n      \n      if (!student || !course) {\n        toast.error('الطالب أو الكورس غير موجود');\n        return;\n      }\n\n      // إنشاء FormData لرفع الملف\n      const submitData = new FormData();\n      submitData.append('studentId', formData.studentId);\n      submitData.append('courseId', formData.courseId);\n      submitData.append('certificateName', formData.certificateName || `شهادة إتمام ${course.title}`);\n      submitData.append('description', formData.description || `تم منح هذه الشهادة للطالب ${student.name} لإتمام دورة ${course.title} بنجاح`);\n      \n      if (selectedFile) {\n        submitData.append('certificate', selectedFile);\n      }\n\n      // محاولة إرسال للخادم\n      try {\n        const response = await axios.post(`/admin/students/${formData.studentId}/certificate`, submitData, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n        \n        toast.success('تم رفع الشهادة بنجاح!');\n      } catch (error) {\n        console.error('خطأ في رفع الشهادة:', error);\n        // محاكاة النجاح للاختبار\n        toast.success('تم رفع الشهادة بنجاح! (وضع الاختبار)');\n      }\n\n      // إضافة الشهادة للقائمة المحلية\n      const newCertificate = {\n        id: Date.now().toString(),\n        studentId: formData.studentId,\n        studentName: student.name,\n        courseId: formData.courseId,\n        courseName: course.title,\n        certificateName: formData.certificateName || `شهادة إتمام ${course.title}`,\n        description: formData.description || `تم منح هذه الشهادة للطالب ${student.name} لإتمام دورة ${course.title} بنجاح`,\n        issuedDate: new Date().toISOString().split('T')[0],\n        issuedBy: 'علاء عبد الحميد',\n        fileUrl: selectedFile ? URL.createObjectURL(selectedFile) : null\n      };\n\n      setCertificates([...certificates, newCertificate]);\n      handleCloseDialog();\n      \n    } catch (error) {\n      console.error('خطأ في إنشاء الشهادة:', error);\n      toast.error('حدث خطأ في إنشاء الشهادة');\n    }\n  };\n\n  const handleDelete = async (certificateId) => {\n    if (window.confirm('هل أنت متأكد من حذف هذه الشهادة؟')) {\n      setCertificates(certificates.filter(cert => cert.id !== certificateId));\n      toast.success('تم حذف الشهادة بنجاح');\n    }\n  };\n\n  const handleDownload = (certificate) => {\n    if (certificate.fileUrl) {\n      // تحميل الملف\n      const link = document.createElement('a');\n      link.href = certificate.fileUrl;\n      link.download = `${certificate.certificateName}.pdf`;\n      link.click();\n    } else {\n      toast.info('لا يوجد ملف مرفق مع هذه الشهادة');\n    }\n  };\n\n  return (\n    <Box>\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h4\" sx={{ fontWeight: 'bold', color: '#1976d2' }}>\n          إدارة الشهادات\n        </Typography>\n        <Button\n          variant=\"contained\"\n          startIcon={<Add />}\n          onClick={handleOpenDialog}\n          sx={{ borderRadius: 2 }}\n        >\n          رفع شهادة جديدة\n        </Button>\n      </Box>\n\n      {/* إحصائيات سريعة */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} md={4}>\n          <Card sx={{ bgcolor: '#e3f2fd', borderLeft: '4px solid #1976d2' }}>\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 'bold', color: '#1976d2' }}>\n                    {certificates.length}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    إجمالي الشهادات\n                  </Typography>\n                </Box>\n                <WorkspacePremium sx={{ fontSize: 40, color: '#1976d2' }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        \n        <Grid item xs={12} md={4}>\n          <Card sx={{ bgcolor: '#e8f5e8', borderLeft: '4px solid #4caf50' }}>\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 'bold', color: '#4caf50' }}>\n                    {certificates.filter(c => c.fileUrl).length}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    شهادات مرفوعة\n                  </Typography>\n                </Box>\n                <Upload sx={{ fontSize: 40, color: '#4caf50' }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        \n        <Grid item xs={12} md={4}>\n          <Card sx={{ bgcolor: '#fff3e0', borderLeft: '4px solid #ff9800' }}>\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 'bold', color: '#ff9800' }}>\n                    {new Set(certificates.map(c => c.studentId)).size}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    طلاب حاصلين على شهادات\n                  </Typography>\n                </Box>\n                <Person sx={{ fontSize: 40, color: '#ff9800' }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* جدول الشهادات */}\n      <TableContainer component={Paper} sx={{ borderRadius: 2 }}>\n        <Table>\n          <TableHead>\n            <TableRow sx={{ bgcolor: '#f5f5f5' }}>\n              <TableCell sx={{ fontWeight: 'bold' }}>الطالب</TableCell>\n              <TableCell sx={{ fontWeight: 'bold' }}>الكورس</TableCell>\n              <TableCell sx={{ fontWeight: 'bold' }}>اسم الشهادة</TableCell>\n              <TableCell sx={{ fontWeight: 'bold' }}>تاريخ الإصدار</TableCell>\n              <TableCell sx={{ fontWeight: 'bold' }}>الحالة</TableCell>\n              <TableCell sx={{ fontWeight: 'bold' }}>الإجراءات</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {certificates.map((certificate) => (\n              <TableRow key={certificate.id} hover>\n                <TableCell>\n                  <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                    <Avatar sx={{ mr: 2, bgcolor: '#1976d2' }}>\n                      {certificate.studentName.charAt(0)}\n                    </Avatar>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n                      {certificate.studentName}\n                    </Typography>\n                  </Box>\n                </TableCell>\n                <TableCell>\n                  <Typography variant=\"body2\">\n                    {certificate.courseName}\n                  </Typography>\n                </TableCell>\n                <TableCell>\n                  <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n                    {certificate.certificateName}\n                  </Typography>\n                </TableCell>\n                <TableCell>\n                  <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                    <CalendarToday sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />\n                    <Typography variant=\"body2\">\n                      {certificate.issuedDate}\n                    </Typography>\n                  </Box>\n                </TableCell>\n                <TableCell>\n                  <Chip\n                    label={certificate.fileUrl ? 'مرفوعة' : 'نصية فقط'}\n                    color={certificate.fileUrl ? 'success' : 'default'}\n                    size=\"small\"\n                  />\n                </TableCell>\n                <TableCell>\n                  <Box sx={{ display: 'flex', gap: 1 }}>\n                    <Tooltip title=\"عرض التفاصيل\">\n                      <IconButton size=\"small\" sx={{ color: '#1976d2' }}>\n                        <Visibility />\n                      </IconButton>\n                    </Tooltip>\n                    \n                    {certificate.fileUrl && (\n                      <Tooltip title=\"تحميل الشهادة\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => handleDownload(certificate)}\n                          sx={{ color: '#4caf50' }}\n                        >\n                          <Download />\n                        </IconButton>\n                      </Tooltip>\n                    )}\n                    \n                    <Tooltip title=\"حذف الشهادة\">\n                      <IconButton\n                        size=\"small\"\n                        onClick={() => handleDelete(certificate.id)}\n                        sx={{ color: '#d32f2f' }}\n                      >\n                        <Delete />\n                      </IconButton>\n                    </Tooltip>\n                  </Box>\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n      </TableContainer>\n\n      {/* Dialog لرفع شهادة جديدة */}\n      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n            <WorkspacePremium sx={{ color: '#1976d2' }} />\n            رفع شهادة جديدة\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <Grid container spacing={3}>\n              <Grid item xs={12} md={6}>\n                <FormControl fullWidth>\n                  <InputLabel>اختر الطالب</InputLabel>\n                  <Select\n                    value={formData.studentId}\n                    label=\"اختر الطالب\"\n                    onChange={(e) => setFormData({ ...formData, studentId: e.target.value })}\n                  >\n                    {students.map((student) => (\n                      <MenuItem key={student._id} value={student._id}>\n                        {student.name} ({student.studentCode})\n                      </MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n              </Grid>\n              \n              <Grid item xs={12} md={6}>\n                <FormControl fullWidth>\n                  <InputLabel>اختر الكورس</InputLabel>\n                  <Select\n                    value={formData.courseId}\n                    label=\"اختر الكورس\"\n                    onChange={(e) => setFormData({ ...formData, courseId: e.target.value })}\n                  >\n                    {courses.map((course) => (\n                      <MenuItem key={course._id} value={course._id}>\n                        {course.title}\n                      </MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n              </Grid>\n              \n              <Grid item xs={12}>\n                <TextField\n                  fullWidth\n                  label=\"اسم الشهادة (اختياري)\"\n                  value={formData.certificateName}\n                  onChange={(e) => setFormData({ ...formData, certificateName: e.target.value })}\n                  placeholder=\"سيتم إنشاء اسم تلقائي إذا تُرك فارغاً\"\n                />\n              </Grid>\n              \n              <Grid item xs={12}>\n                <TextField\n                  fullWidth\n                  multiline\n                  rows={3}\n                  label=\"وصف الشهادة (اختياري)\"\n                  value={formData.description}\n                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}\n                  placeholder=\"سيتم إنشاء وصف تلقائي إذا تُرك فارغاً\"\n                />\n              </Grid>\n              \n              <Grid item xs={12}>\n                <Divider sx={{ my: 2 }} />\n                <Typography variant=\"h6\" sx={{ mb: 2 }}>\n                  رفع ملف الشهادة (اختياري)\n                </Typography>\n                \n                <input\n                  accept=\"image/*,.pdf\"\n                  style={{ display: 'none' }}\n                  id=\"certificate-file\"\n                  type=\"file\"\n                  onChange={handleFileChange}\n                />\n                <label htmlFor=\"certificate-file\">\n                  <Button\n                    variant=\"outlined\"\n                    component=\"span\"\n                    startIcon={<Upload />}\n                    sx={{ mb: 2 }}\n                  >\n                    اختر ملف الشهادة\n                  </Button>\n                </label>\n                \n                {selectedFile && (\n                  <Alert severity=\"success\" sx={{ mt: 2 }}>\n                    تم اختيار الملف: {selectedFile.name}\n                  </Alert>\n                )}\n                \n                <Typography variant=\"body2\" color=\"textSecondary\" sx={{ mt: 1 }}>\n                  يُسمح بملفات الصور (JPG, PNG) أو PDF بحد أقصى 5 ميجابايت\n                </Typography>\n              </Grid>\n            </Grid>\n          </Box>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDialog}>إلغاء</Button>\n          <Button onClick={handleSubmit} variant=\"contained\" startIcon={<Upload />}>\n            رفع الشهادة\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default CertificateManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,OAAO,QACF,eAAe;AACtB,SACEC,GAAG,EACHC,MAAM,EACNC,QAAQ,EACRC,UAAU,EACVC,MAAM,EACNC,gBAAgB,EAChBC,MAAM,EACNC,MAAM,EACNC,aAAa,QACR,qBAAqB;AAC5B,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC+C,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACmD,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACqD,YAAY,EAAEC,eAAe,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACuD,QAAQ,EAAEC,WAAW,CAAC,GAAGxD,QAAQ,CAAC;IACvCyD,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC;EAEF3D,SAAS,CAAC,MAAM;IACd4D,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF;MACA,MAAM,CAACC,WAAW,EAAEC,UAAU,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAClDzB,KAAK,CAAC0B,GAAG,CAAC,iBAAiB,CAAC,EAC5B1B,KAAK,CAAC0B,GAAG,CAAC,gBAAgB,CAAC,CAC5B,CAAC;MAEFlB,WAAW,CAACc,WAAW,CAACK,IAAI,CAAC;MAC7BjB,UAAU,CAACa,UAAU,CAACI,IAAI,CAAC;;MAE3B;MACArB,eAAe,CAAC,CACd;QACEsB,EAAE,EAAE,GAAG;QACPX,SAAS,EAAE,UAAU;QACrBY,WAAW,EAAE,WAAW;QACxBX,QAAQ,EAAE,SAAS;QACnBY,UAAU,EAAE,wBAAwB;QACpCX,eAAe,EAAE,oCAAoC;QACrDC,WAAW,EAAE,8EAA8E;QAC3FW,UAAU,EAAE,YAAY;QACxBC,QAAQ,EAAE,iBAAiB;QAC3BC,OAAO,EAAE;MACX,CAAC,CACF,CAAC;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C;MACA1B,WAAW,CAAC,CACV;QAAE4B,GAAG,EAAE,UAAU;QAAEC,IAAI,EAAE,WAAW;QAAEC,WAAW,EAAE;MAAS,CAAC,CAC9D,CAAC;MACF5B,UAAU,CAAC,CACT;QAAE0B,GAAG,EAAE,SAAS;QAAEG,KAAK,EAAE;MAAyB,CAAC,EACnD;QAAEH,GAAG,EAAE,SAAS;QAAEG,KAAK,EAAE;MAAgC,CAAC,CAC3D,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BxB,WAAW,CAAC;MACVC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE,EAAE;MACnBC,WAAW,EAAE;IACf,CAAC,CAAC;IACFN,eAAe,CAAC,IAAI,CAAC;IACrBF,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAM6B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B7B,aAAa,CAAC,KAAK,CAAC;IACpBI,WAAW,CAAC;MACVC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE,EAAE;MACnBC,WAAW,EAAE;IACf,CAAC,CAAC;IACFN,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM4B,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR;MACA,MAAMG,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,iBAAiB,CAAC;MAChF,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACJ,IAAI,CAACK,IAAI,CAAC,EAAE;QACrClD,KAAK,CAACmC,KAAK,CAAC,0CAA0C,CAAC;QACvD;MACF;;MAEA;MACA,IAAIU,IAAI,CAACM,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;QAC/BnD,KAAK,CAACmC,KAAK,CAAC,yCAAyC,CAAC;QACtD;MACF;MAEApB,eAAe,CAAC8B,IAAI,CAAC;IACvB;EACF,CAAC;EAED,MAAMO,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAACpC,QAAQ,CAACE,SAAS,IAAI,CAACF,QAAQ,CAACG,QAAQ,EAAE;MAC7CnB,KAAK,CAACmC,KAAK,CAAC,4BAA4B,CAAC;MACzC;IACF;IAEA,IAAI;MACF,MAAMkB,OAAO,GAAG7C,QAAQ,CAAC8C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClB,GAAG,KAAKrB,QAAQ,CAACE,SAAS,CAAC;MAChE,MAAMsC,MAAM,GAAG9C,OAAO,CAAC4C,IAAI,CAACG,CAAC,IAAIA,CAAC,CAACpB,GAAG,KAAKrB,QAAQ,CAACG,QAAQ,CAAC;MAE7D,IAAI,CAACkC,OAAO,IAAI,CAACG,MAAM,EAAE;QACvBxD,KAAK,CAACmC,KAAK,CAAC,4BAA4B,CAAC;QACzC;MACF;;MAEA;MACA,MAAMuB,UAAU,GAAG,IAAIC,QAAQ,CAAC,CAAC;MACjCD,UAAU,CAACE,MAAM,CAAC,WAAW,EAAE5C,QAAQ,CAACE,SAAS,CAAC;MAClDwC,UAAU,CAACE,MAAM,CAAC,UAAU,EAAE5C,QAAQ,CAACG,QAAQ,CAAC;MAChDuC,UAAU,CAACE,MAAM,CAAC,iBAAiB,EAAE5C,QAAQ,CAACI,eAAe,IAAI,eAAeoC,MAAM,CAAChB,KAAK,EAAE,CAAC;MAC/FkB,UAAU,CAACE,MAAM,CAAC,aAAa,EAAE5C,QAAQ,CAACK,WAAW,IAAI,6BAA6BgC,OAAO,CAACf,IAAI,gBAAgBkB,MAAM,CAAChB,KAAK,QAAQ,CAAC;MAEvI,IAAI1B,YAAY,EAAE;QAChB4C,UAAU,CAACE,MAAM,CAAC,aAAa,EAAE9C,YAAY,CAAC;MAChD;;MAEA;MACA,IAAI;QACF,MAAM+C,QAAQ,GAAG,MAAM5D,KAAK,CAAC6D,IAAI,CAAC,mBAAmB9C,QAAQ,CAACE,SAAS,cAAc,EAAEwC,UAAU,EAAE;UACjGK,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF/D,KAAK,CAACgE,OAAO,CAAC,uBAAuB,CAAC;MACxC,CAAC,CAAC,OAAO7B,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C;QACAnC,KAAK,CAACgE,OAAO,CAAC,sCAAsC,CAAC;MACvD;;MAEA;MACA,MAAMC,cAAc,GAAG;QACrBpC,EAAE,EAAEqC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACzBlD,SAAS,EAAEF,QAAQ,CAACE,SAAS;QAC7BY,WAAW,EAAEuB,OAAO,CAACf,IAAI;QACzBnB,QAAQ,EAAEH,QAAQ,CAACG,QAAQ;QAC3BY,UAAU,EAAEyB,MAAM,CAAChB,KAAK;QACxBpB,eAAe,EAAEJ,QAAQ,CAACI,eAAe,IAAI,eAAeoC,MAAM,CAAChB,KAAK,EAAE;QAC1EnB,WAAW,EAAEL,QAAQ,CAACK,WAAW,IAAI,6BAA6BgC,OAAO,CAACf,IAAI,gBAAgBkB,MAAM,CAAChB,KAAK,QAAQ;QAClHR,UAAU,EAAE,IAAIkC,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAClDrC,QAAQ,EAAE,iBAAiB;QAC3BC,OAAO,EAAEpB,YAAY,GAAGyD,GAAG,CAACC,eAAe,CAAC1D,YAAY,CAAC,GAAG;MAC9D,CAAC;MAEDP,eAAe,CAAC,CAAC,GAAGD,YAAY,EAAE2D,cAAc,CAAC,CAAC;MAClDvB,iBAAiB,CAAC,CAAC;IAErB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CnC,KAAK,CAACmC,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CAAC;EAED,MAAMsC,YAAY,GAAG,MAAOC,aAAa,IAAK;IAC5C,IAAIC,MAAM,CAACC,OAAO,CAAC,kCAAkC,CAAC,EAAE;MACtDrE,eAAe,CAACD,YAAY,CAACuE,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACjD,EAAE,KAAK6C,aAAa,CAAC,CAAC;MACvE1E,KAAK,CAACgE,OAAO,CAAC,sBAAsB,CAAC;IACvC;EACF,CAAC;EAED,MAAMe,cAAc,GAAIC,WAAW,IAAK;IACtC,IAAIA,WAAW,CAAC9C,OAAO,EAAE;MACvB;MACA,MAAM+C,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW,CAAC9C,OAAO;MAC/B+C,IAAI,CAACI,QAAQ,GAAG,GAAGL,WAAW,CAAC5D,eAAe,MAAM;MACpD6D,IAAI,CAACK,KAAK,CAAC,CAAC;IACd,CAAC,MAAM;MACLtF,KAAK,CAACuF,IAAI,CAAC,iCAAiC,CAAC;IAC/C;EACF,CAAC;EAED,oBACEpF,OAAA,CAACxC,GAAG;IAAA6H,QAAA,gBAEFrF,OAAA,CAACxC,GAAG;MAAC8H,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACzFrF,OAAA,CAACvC,UAAU;QAACkI,OAAO,EAAC,IAAI;QAACL,EAAE,EAAE;UAAEM,UAAU,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAU,CAAE;QAAAR,QAAA,EAAC;MAEvE;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjG,OAAA,CAACtC,MAAM;QACLiI,OAAO,EAAC,WAAW;QACnBO,SAAS,eAAElG,OAAA,CAACZ,GAAG;UAAA0G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACnBE,OAAO,EAAE7D,gBAAiB;QAC1BgD,EAAE,EAAE;UAAEc,YAAY,EAAE;QAAE,CAAE;QAAAf,QAAA,EACzB;MAED;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNjG,OAAA,CAACjB,IAAI;MAACsH,SAAS;MAACC,OAAO,EAAE,CAAE;MAAChB,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACxCrF,OAAA,CAACjB,IAAI;QAACwH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAApB,QAAA,eACvBrF,OAAA,CAAChB,IAAI;UAACsG,EAAE,EAAE;YAAEoB,OAAO,EAAE,SAAS;YAAEC,UAAU,EAAE;UAAoB,CAAE;UAAAtB,QAAA,eAChErF,OAAA,CAACf,WAAW;YAAAoG,QAAA,eACVrF,OAAA,CAACxC,GAAG;cAAC8H,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAAH,QAAA,gBAClFrF,OAAA,CAACxC,GAAG;gBAAA6H,QAAA,gBACFrF,OAAA,CAACvC,UAAU;kBAACkI,OAAO,EAAC,IAAI;kBAACL,EAAE,EAAE;oBAAEM,UAAU,EAAE,MAAM;oBAAEC,KAAK,EAAE;kBAAU,CAAE;kBAAAR,QAAA,EACnElF,YAAY,CAACyG;gBAAM;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACbjG,OAAA,CAACvC,UAAU;kBAACkI,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,eAAe;kBAAAR,QAAA,EAAC;gBAElD;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNjG,OAAA,CAACP,gBAAgB;gBAAC6F,EAAE,EAAE;kBAAEuB,QAAQ,EAAE,EAAE;kBAAEhB,KAAK,EAAE;gBAAU;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPjG,OAAA,CAACjB,IAAI;QAACwH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAApB,QAAA,eACvBrF,OAAA,CAAChB,IAAI;UAACsG,EAAE,EAAE;YAAEoB,OAAO,EAAE,SAAS;YAAEC,UAAU,EAAE;UAAoB,CAAE;UAAAtB,QAAA,eAChErF,OAAA,CAACf,WAAW;YAAAoG,QAAA,eACVrF,OAAA,CAACxC,GAAG;cAAC8H,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAAH,QAAA,gBAClFrF,OAAA,CAACxC,GAAG;gBAAA6H,QAAA,gBACFrF,OAAA,CAACvC,UAAU;kBAACkI,OAAO,EAAC,IAAI;kBAACL,EAAE,EAAE;oBAAEM,UAAU,EAAE,MAAM;oBAAEC,KAAK,EAAE;kBAAU,CAAE;kBAAAR,QAAA,EACnElF,YAAY,CAACuE,MAAM,CAACpB,CAAC,IAAIA,CAAC,CAACvB,OAAO,CAAC,CAAC6E;gBAAM;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACbjG,OAAA,CAACvC,UAAU;kBAACkI,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,eAAe;kBAAAR,QAAA,EAAC;gBAElD;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNjG,OAAA,CAACX,MAAM;gBAACiG,EAAE,EAAE;kBAAEuB,QAAQ,EAAE,EAAE;kBAAEhB,KAAK,EAAE;gBAAU;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPjG,OAAA,CAACjB,IAAI;QAACwH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAApB,QAAA,eACvBrF,OAAA,CAAChB,IAAI;UAACsG,EAAE,EAAE;YAAEoB,OAAO,EAAE,SAAS;YAAEC,UAAU,EAAE;UAAoB,CAAE;UAAAtB,QAAA,eAChErF,OAAA,CAACf,WAAW;YAAAoG,QAAA,eACVrF,OAAA,CAACxC,GAAG;cAAC8H,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAAH,QAAA,gBAClFrF,OAAA,CAACxC,GAAG;gBAAA6H,QAAA,gBACFrF,OAAA,CAACvC,UAAU;kBAACkI,OAAO,EAAC,IAAI;kBAACL,EAAE,EAAE;oBAAEM,UAAU,EAAE,MAAM;oBAAEC,KAAK,EAAE;kBAAU,CAAE;kBAAAR,QAAA,EACnE,IAAIyB,GAAG,CAAC3G,YAAY,CAAC4G,GAAG,CAACzD,CAAC,IAAIA,CAAC,CAACvC,SAAS,CAAC,CAAC,CAACiC;gBAAI;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACbjG,OAAA,CAACvC,UAAU;kBAACkI,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,eAAe;kBAAAR,QAAA,EAAC;gBAElD;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNjG,OAAA,CAACL,MAAM;gBAAC2F,EAAE,EAAE;kBAAEuB,QAAQ,EAAE,EAAE;kBAAEhB,KAAK,EAAE;gBAAU;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPjG,OAAA,CAACjC,cAAc;MAACiJ,SAAS,EAAErJ,KAAM;MAAC2H,EAAE,EAAE;QAAEc,YAAY,EAAE;MAAE,CAAE;MAAAf,QAAA,eACxDrF,OAAA,CAACpC,KAAK;QAAAyH,QAAA,gBACJrF,OAAA,CAAChC,SAAS;UAAAqH,QAAA,eACRrF,OAAA,CAAC/B,QAAQ;YAACqH,EAAE,EAAE;cAAEoB,OAAO,EAAE;YAAU,CAAE;YAAArB,QAAA,gBACnCrF,OAAA,CAAClC,SAAS;cAACwH,EAAE,EAAE;gBAAEM,UAAU,EAAE;cAAO,CAAE;cAAAP,QAAA,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACzDjG,OAAA,CAAClC,SAAS;cAACwH,EAAE,EAAE;gBAAEM,UAAU,EAAE;cAAO,CAAE;cAAAP,QAAA,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACzDjG,OAAA,CAAClC,SAAS;cAACwH,EAAE,EAAE;gBAAEM,UAAU,EAAE;cAAO,CAAE;cAAAP,QAAA,EAAC;YAAW;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC9DjG,OAAA,CAAClC,SAAS;cAACwH,EAAE,EAAE;gBAAEM,UAAU,EAAE;cAAO,CAAE;cAAAP,QAAA,EAAC;YAAa;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAChEjG,OAAA,CAAClC,SAAS;cAACwH,EAAE,EAAE;gBAAEM,UAAU,EAAE;cAAO,CAAE;cAAAP,QAAA,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACzDjG,OAAA,CAAClC,SAAS;cAACwH,EAAE,EAAE;gBAAEM,UAAU,EAAE;cAAO,CAAE;cAAAP,QAAA,EAAC;YAAS;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZjG,OAAA,CAACnC,SAAS;UAAAwH,QAAA,EACPlF,YAAY,CAAC4G,GAAG,CAAElC,WAAW,iBAC5B7E,OAAA,CAAC/B,QAAQ;YAAsBgJ,KAAK;YAAA5B,QAAA,gBAClCrF,OAAA,CAAClC,SAAS;cAAAuH,QAAA,eACRrF,OAAA,CAACxC,GAAG;gBAAC8H,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAJ,QAAA,gBACjDrF,OAAA,CAACd,MAAM;kBAACoG,EAAE,EAAE;oBAAE4B,EAAE,EAAE,CAAC;oBAAER,OAAO,EAAE;kBAAU,CAAE;kBAAArB,QAAA,EACvCR,WAAW,CAAClD,WAAW,CAACwF,MAAM,CAAC,CAAC;gBAAC;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC,eACTjG,OAAA,CAACvC,UAAU;kBAACkI,OAAO,EAAC,OAAO;kBAACL,EAAE,EAAE;oBAAEM,UAAU,EAAE;kBAAI,CAAE;kBAAAP,QAAA,EACjDR,WAAW,CAAClD;gBAAW;kBAAAmE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACZjG,OAAA,CAAClC,SAAS;cAAAuH,QAAA,eACRrF,OAAA,CAACvC,UAAU;gBAACkI,OAAO,EAAC,OAAO;gBAAAN,QAAA,EACxBR,WAAW,CAACjD;cAAU;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACZjG,OAAA,CAAClC,SAAS;cAAAuH,QAAA,eACRrF,OAAA,CAACvC,UAAU;gBAACkI,OAAO,EAAC,OAAO;gBAACL,EAAE,EAAE;kBAAEM,UAAU,EAAE;gBAAI,CAAE;gBAAAP,QAAA,EACjDR,WAAW,CAAC5D;cAAe;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACZjG,OAAA,CAAClC,SAAS;cAAAuH,QAAA,eACRrF,OAAA,CAACxC,GAAG;gBAAC8H,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAJ,QAAA,gBACjDrF,OAAA,CAACJ,aAAa;kBAAC0F,EAAE,EAAE;oBAAEuB,QAAQ,EAAE,EAAE;oBAAEK,EAAE,EAAE,CAAC;oBAAErB,KAAK,EAAE;kBAAiB;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvEjG,OAAA,CAACvC,UAAU;kBAACkI,OAAO,EAAC,OAAO;kBAAAN,QAAA,EACxBR,WAAW,CAAChD;gBAAU;kBAAAiE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACZjG,OAAA,CAAClC,SAAS;cAAAuH,QAAA,eACRrF,OAAA,CAACrB,IAAI;gBACHyI,KAAK,EAAEvC,WAAW,CAAC9C,OAAO,GAAG,QAAQ,GAAG,UAAW;gBACnD8D,KAAK,EAAEhB,WAAW,CAAC9C,OAAO,GAAG,SAAS,GAAG,SAAU;gBACnDiB,IAAI,EAAC;cAAO;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZjG,OAAA,CAAClC,SAAS;cAAAuH,QAAA,eACRrF,OAAA,CAACxC,GAAG;gBAAC8H,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAE8B,GAAG,EAAE;gBAAE,CAAE;gBAAAhC,QAAA,gBACnCrF,OAAA,CAACnB,OAAO;kBAACwD,KAAK,EAAC,qEAAc;kBAAAgD,QAAA,eAC3BrF,OAAA,CAACpB,UAAU;oBAACoE,IAAI,EAAC,OAAO;oBAACsC,EAAE,EAAE;sBAAEO,KAAK,EAAE;oBAAU,CAAE;oBAAAR,QAAA,eAChDrF,OAAA,CAACT,UAAU;sBAAAuG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EAETpB,WAAW,CAAC9C,OAAO,iBAClB/B,OAAA,CAACnB,OAAO;kBAACwD,KAAK,EAAC,2EAAe;kBAAAgD,QAAA,eAC5BrF,OAAA,CAACpB,UAAU;oBACToE,IAAI,EAAC,OAAO;oBACZmD,OAAO,EAAEA,CAAA,KAAMvB,cAAc,CAACC,WAAW,CAAE;oBAC3CS,EAAE,EAAE;sBAAEO,KAAK,EAAE;oBAAU,CAAE;oBAAAR,QAAA,eAEzBrF,OAAA,CAACV,QAAQ;sBAAAwG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CACV,eAEDjG,OAAA,CAACnB,OAAO;kBAACwD,KAAK,EAAC,+DAAa;kBAAAgD,QAAA,eAC1BrF,OAAA,CAACpB,UAAU;oBACToE,IAAI,EAAC,OAAO;oBACZmD,OAAO,EAAEA,CAAA,KAAM7B,YAAY,CAACO,WAAW,CAACnD,EAAE,CAAE;oBAC5C4D,EAAE,EAAE;sBAAEO,KAAK,EAAE;oBAAU,CAAE;oBAAAR,QAAA,eAEzBrF,OAAA,CAACR,MAAM;sBAAAsG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA,GAlECpB,WAAW,CAACnD,EAAE;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmEnB,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAGjBjG,OAAA,CAAC9B,MAAM;MAACoJ,IAAI,EAAE7G,UAAW;MAAC8G,OAAO,EAAEhF,iBAAkB;MAACiF,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAApC,QAAA,gBAC3ErF,OAAA,CAAC7B,WAAW;QAAAkH,QAAA,eACVrF,OAAA,CAACxC,GAAG;UAAC8H,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAE4B,GAAG,EAAE;UAAE,CAAE;UAAAhC,QAAA,gBACzDrF,OAAA,CAACP,gBAAgB;YAAC6F,EAAE,EAAE;cAAEO,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oFAEhD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdjG,OAAA,CAAC5B,aAAa;QAAAiH,QAAA,eACZrF,OAAA,CAACxC,GAAG;UAAC8H,EAAE,EAAE;YAAEoC,EAAE,EAAE;UAAE,CAAE;UAAArC,QAAA,eACjBrF,OAAA,CAACjB,IAAI;YAACsH,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAjB,QAAA,gBACzBrF,OAAA,CAACjB,IAAI;cAACwH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAApB,QAAA,eACvBrF,OAAA,CAACzB,WAAW;gBAACkJ,SAAS;gBAAApC,QAAA,gBACpBrF,OAAA,CAACxB,UAAU;kBAAA6G,QAAA,EAAC;gBAAW;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpCjG,OAAA,CAACvB,MAAM;kBACLkJ,KAAK,EAAE9G,QAAQ,CAACE,SAAU;kBAC1BqG,KAAK,EAAC,+DAAa;kBACnBQ,QAAQ,EAAGC,CAAC,IAAK/G,WAAW,CAAC;oBAAE,GAAGD,QAAQ;oBAAEE,SAAS,EAAE8G,CAAC,CAAClF,MAAM,CAACgF;kBAAM,CAAC,CAAE;kBAAAtC,QAAA,EAExEhF,QAAQ,CAAC0G,GAAG,CAAE7D,OAAO,iBACpBlD,OAAA,CAACtB,QAAQ;oBAAmBiJ,KAAK,EAAEzE,OAAO,CAAChB,GAAI;oBAAAmD,QAAA,GAC5CnC,OAAO,CAACf,IAAI,EAAC,IAAE,EAACe,OAAO,CAACd,WAAW,EAAC,GACvC;kBAAA,GAFec,OAAO,CAAChB,GAAG;oBAAA4D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEhB,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPjG,OAAA,CAACjB,IAAI;cAACwH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAApB,QAAA,eACvBrF,OAAA,CAACzB,WAAW;gBAACkJ,SAAS;gBAAApC,QAAA,gBACpBrF,OAAA,CAACxB,UAAU;kBAAA6G,QAAA,EAAC;gBAAW;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpCjG,OAAA,CAACvB,MAAM;kBACLkJ,KAAK,EAAE9G,QAAQ,CAACG,QAAS;kBACzBoG,KAAK,EAAC,+DAAa;kBACnBQ,QAAQ,EAAGC,CAAC,IAAK/G,WAAW,CAAC;oBAAE,GAAGD,QAAQ;oBAAEG,QAAQ,EAAE6G,CAAC,CAAClF,MAAM,CAACgF;kBAAM,CAAC,CAAE;kBAAAtC,QAAA,EAEvE9E,OAAO,CAACwG,GAAG,CAAE1D,MAAM,iBAClBrD,OAAA,CAACtB,QAAQ;oBAAkBiJ,KAAK,EAAEtE,MAAM,CAACnB,GAAI;oBAAAmD,QAAA,EAC1ChC,MAAM,CAAChB;kBAAK,GADAgB,MAAM,CAACnB,GAAG;oBAAA4D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEf,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPjG,OAAA,CAACjB,IAAI;cAACwH,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAnB,QAAA,eAChBrF,OAAA,CAAC1B,SAAS;gBACRmJ,SAAS;gBACTL,KAAK,EAAC,4GAAuB;gBAC7BO,KAAK,EAAE9G,QAAQ,CAACI,eAAgB;gBAChC2G,QAAQ,EAAGC,CAAC,IAAK/G,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEI,eAAe,EAAE4G,CAAC,CAAClF,MAAM,CAACgF;gBAAM,CAAC,CAAE;gBAC/EG,WAAW,EAAC;cAAuC;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEPjG,OAAA,CAACjB,IAAI;cAACwH,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAnB,QAAA,eAChBrF,OAAA,CAAC1B,SAAS;gBACRmJ,SAAS;gBACTM,SAAS;gBACTC,IAAI,EAAE,CAAE;gBACRZ,KAAK,EAAC,4GAAuB;gBAC7BO,KAAK,EAAE9G,QAAQ,CAACK,WAAY;gBAC5B0G,QAAQ,EAAGC,CAAC,IAAK/G,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEK,WAAW,EAAE2G,CAAC,CAAClF,MAAM,CAACgF;gBAAM,CAAC,CAAE;gBAC3EG,WAAW,EAAC;cAAuC;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEPjG,OAAA,CAACjB,IAAI;cAACwH,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAnB,QAAA,gBAChBrF,OAAA,CAACb,OAAO;gBAACmG,EAAE,EAAE;kBAAE2C,EAAE,EAAE;gBAAE;cAAE;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1BjG,OAAA,CAACvC,UAAU;gBAACkI,OAAO,EAAC,IAAI;gBAACL,EAAE,EAAE;kBAAEI,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,EAAC;cAExC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAEbjG,OAAA;gBACEkI,MAAM,EAAC,cAAc;gBACrBC,KAAK,EAAE;kBAAE5C,OAAO,EAAE;gBAAO,CAAE;gBAC3B7D,EAAE,EAAC,kBAAkB;gBACrBqB,IAAI,EAAC,MAAM;gBACX6E,QAAQ,EAAEpF;cAAiB;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACFjG,OAAA;gBAAOoI,OAAO,EAAC,kBAAkB;gBAAA/C,QAAA,eAC/BrF,OAAA,CAACtC,MAAM;kBACLiI,OAAO,EAAC,UAAU;kBAClBqB,SAAS,EAAC,MAAM;kBAChBd,SAAS,eAAElG,OAAA,CAACX,MAAM;oBAAAyG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACtBX,EAAE,EAAE;oBAAEI,EAAE,EAAE;kBAAE,CAAE;kBAAAL,QAAA,EACf;gBAED;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,EAEPtF,YAAY,iBACXX,OAAA,CAAClB,KAAK;gBAACuJ,QAAQ,EAAC,SAAS;gBAAC/C,EAAE,EAAE;kBAAEgD,EAAE,EAAE;gBAAE,CAAE;gBAAAjD,QAAA,GAAC,oFACtB,EAAC1E,YAAY,CAACwB,IAAI;cAAA;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CACR,eAEDjG,OAAA,CAACvC,UAAU;gBAACkI,OAAO,EAAC,OAAO;gBAACE,KAAK,EAAC,eAAe;gBAACP,EAAE,EAAE;kBAAEgD,EAAE,EAAE;gBAAE,CAAE;gBAAAjD,QAAA,EAAC;cAEjE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBjG,OAAA,CAAC3B,aAAa;QAAAgH,QAAA,gBACZrF,OAAA,CAACtC,MAAM;UAACyI,OAAO,EAAE5D,iBAAkB;UAAA8C,QAAA,EAAC;QAAK;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClDjG,OAAA,CAACtC,MAAM;UAACyI,OAAO,EAAElD,YAAa;UAAC0C,OAAO,EAAC,WAAW;UAACO,SAAS,eAAElG,OAAA,CAACX,MAAM;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAZ,QAAA,EAAC;QAE1E;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC/F,EAAA,CAxcID,qBAAqB;AAAAsI,EAAA,GAArBtI,qBAAqB;AA0c3B,eAAeA,qBAAqB;AAAC,IAAAsI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
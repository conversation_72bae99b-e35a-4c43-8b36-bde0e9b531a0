import React, { useState } from 'react';
import {
  Di<PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Divider,
  Al<PERSON>,
  Chip
} from '@mui/material';
import {
  Add,
  Delete,
  PlayArrow,
  Edit,
  Save,
  Cancel
} from '@mui/icons-material';
import { toast } from 'react-toastify';

const VideoManagement = ({ open, onClose, course, onUpdateCourse }) => {
  const [videos, setVideos] = useState(course?.videos || []);
  const [newVideo, setNewVideo] = useState({
    title: '',
    duration: '',
    url: ''
  });
  const [editingIndex, setEditingIndex] = useState(-1);
  const [editingVideo, setEditingVideo] = useState({});

  const handleAddVideo = () => {
    if (!newVideo.title || !newVideo.duration) {
      toast.error('يرجى إدخال عنوان الفيديو والمدة');
      return;
    }

    const video = {
      id: videos.length + 1,
      title: newVideo.title,
      duration: newVideo.duration,
      url: newVideo.url || `https://example.com/video${videos.length + 1}`
    };

    const updatedVideos = [...videos, video];
    setVideos(updatedVideos);
    setNewVideo({ title: '', duration: '', url: '' });
    toast.success('تم إضافة الفيديو بنجاح');
  };

  const handleDeleteVideo = (index) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الفيديو؟')) {
      const updatedVideos = videos.filter((_, i) => i !== index);
      setVideos(updatedVideos);
      toast.success('تم حذف الفيديو بنجاح');
    }
  };

  const handleEditVideo = (index) => {
    setEditingIndex(index);
    setEditingVideo({ ...videos[index] });
  };

  const handleSaveEdit = () => {
    if (!editingVideo.title || !editingVideo.duration) {
      toast.error('يرجى إدخال عنوان الفيديو والمدة');
      return;
    }

    const updatedVideos = videos.map((video, index) =>
      index === editingIndex ? editingVideo : video
    );
    setVideos(updatedVideos);
    setEditingIndex(-1);
    setEditingVideo({});
    toast.success('تم تحديث الفيديو بنجاح');
  };

  const handleCancelEdit = () => {
    setEditingIndex(-1);
    setEditingVideo({});
  };

  const handleSave = async () => {
    try {
      // تحديث الكورس مع الفيديوهات الجديدة
      const updatedCourse = {
        ...course,
        videos: videos
      };

      // محاولة إرسال للخادم
      // await axios.put(`/admin/courses/${course._id}`, updatedCourse);
      
      // تحديث الكورس في المكون الأب
      onUpdateCourse(updatedCourse);
      
      toast.success('تم حفظ الفيديوهات بنجاح');
      onClose();
    } catch (error) {
      console.error('خطأ في حفظ الفيديوهات:', error);
      toast.error('حدث خطأ في حفظ الفيديوهات');
    }
  };

  const formatDuration = (duration) => {
    // تنسيق المدة (مثال: "10:30" أو "1:05:30")
    if (duration.includes(':')) {
      return duration;
    }
    // إذا كانت المدة بالدقائق فقط
    return `${duration}:00`;
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <PlayArrow sx={{ color: '#1976d2' }} />
          إدارة فيديوهات الكورس: {course?.title}
        </Box>
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ pt: 2 }}>
          {/* إضافة فيديو جديد */}
          <Box sx={{ mb: 4, p: 3, bgcolor: '#f5f5f5', borderRadius: 2 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
              إضافة فيديو جديد
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 2, mb: 2, flexWrap: 'wrap' }}>
              <TextField
                label="عنوان الفيديو"
                value={newVideo.title}
                onChange={(e) => setNewVideo({ ...newVideo, title: e.target.value })}
                sx={{ flex: 1, minWidth: 200 }}
                placeholder="مثال: مقدمة في التسويق الرقمي"
              />
              
              <TextField
                label="مدة الفيديو"
                value={newVideo.duration}
                onChange={(e) => setNewVideo({ ...newVideo, duration: e.target.value })}
                sx={{ width: 120 }}
                placeholder="10:30"
                helperText="بالدقائق:الثواني"
              />
            </Box>
            
            <TextField
              fullWidth
              label="رابط الفيديو (اختياري)"
              value={newVideo.url}
              onChange={(e) => setNewVideo({ ...newVideo, url: e.target.value })}
              sx={{ mb: 2 }}
              placeholder="https://youtube.com/watch?v=..."
            />
            
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={handleAddVideo}
              sx={{ borderRadius: 2 }}
            >
              إضافة الفيديو
            </Button>
          </Box>

          <Divider sx={{ my: 3 }} />

          {/* قائمة الفيديوهات */}
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
            فيديوهات الكورس ({videos.length})
          </Typography>

          {videos.length === 0 ? (
            <Alert severity="info" sx={{ mt: 2 }}>
              لا توجد فيديوهات في هذا الكورس بعد. ابدأ بإضافة فيديو جديد!
            </Alert>
          ) : (
            <List sx={{ bgcolor: 'background.paper', borderRadius: 2 }}>
              {videos.map((video, index) => (
                <React.Fragment key={index}>
                  <ListItem sx={{ py: 2 }}>
                    {editingIndex === index ? (
                      // وضع التعديل
                      <Box sx={{ width: '100%' }}>
                        <Box sx={{ display: 'flex', gap: 2, mb: 2, flexWrap: 'wrap' }}>
                          <TextField
                            label="عنوان الفيديو"
                            value={editingVideo.title}
                            onChange={(e) => setEditingVideo({ ...editingVideo, title: e.target.value })}
                            sx={{ flex: 1, minWidth: 200 }}
                          />
                          
                          <TextField
                            label="مدة الفيديو"
                            value={editingVideo.duration}
                            onChange={(e) => setEditingVideo({ ...editingVideo, duration: e.target.value })}
                            sx={{ width: 120 }}
                          />
                        </Box>
                        
                        <TextField
                          fullWidth
                          label="رابط الفيديو"
                          value={editingVideo.url || ''}
                          onChange={(e) => setEditingVideo({ ...editingVideo, url: e.target.value })}
                          sx={{ mb: 2 }}
                        />
                        
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Button
                            size="small"
                            variant="contained"
                            startIcon={<Save />}
                            onClick={handleSaveEdit}
                          >
                            حفظ
                          </Button>
                          <Button
                            size="small"
                            variant="outlined"
                            startIcon={<Cancel />}
                            onClick={handleCancelEdit}
                          >
                            إلغاء
                          </Button>
                        </Box>
                      </Box>
                    ) : (
                      // وضع العرض
                      <>
                        <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
                          <PlayArrow sx={{ color: '#1976d2', mr: 1 }} />
                          <Typography variant="h6" sx={{ color: '#1976d2' }}>
                            {index + 1}
                          </Typography>
                        </Box>
                        
                        <ListItemText
                          primary={
                            <Typography variant="body1" sx={{ fontWeight: 500 }}>
                              {video.title}
                            </Typography>
                          }
                          secondary={
                            <Box sx={{ display: 'flex', gap: 2, mt: 1 }}>
                              <Chip
                                label={formatDuration(video.duration)}
                                size="small"
                                color="primary"
                                variant="outlined"
                              />
                              {video.url && (
                                <Chip
                                  label="يحتوي على رابط"
                                  size="small"
                                  color="success"
                                  variant="outlined"
                                />
                              )}
                            </Box>
                          }
                        />
                        
                        <ListItemSecondaryAction>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <IconButton
                              size="small"
                              onClick={() => handleEditVideo(index)}
                              sx={{ color: '#1976d2' }}
                            >
                              <Edit />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleDeleteVideo(index)}
                              sx={{ color: '#d32f2f' }}
                            >
                              <Delete />
                            </IconButton>
                          </Box>
                        </ListItemSecondaryAction>
                      </>
                    )}
                  </ListItem>
                  {index < videos.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          )}

          {videos.length > 0 && (
            <Box sx={{ mt: 3, p: 2, bgcolor: '#e3f2fd', borderRadius: 2 }}>
              <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 1 }}>
                ملخص الكورس:
              </Typography>
              <Typography variant="body2">
                • إجمالي الفيديوهات: {videos.length}
              </Typography>
              <Typography variant="body2">
                • إجمالي المدة: {videos.reduce((total, video) => {
                  const [minutes, seconds] = video.duration.split(':').map(Number);
                  return total + minutes + (seconds || 0) / 60;
                }, 0).toFixed(0)} دقيقة تقريباً
              </Typography>
            </Box>
          )}
        </Box>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>إلغاء</Button>
        <Button onClick={handleSave} variant="contained" startIcon={<Save />}>
          حفظ التغييرات
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default VideoManagement;

import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch,
  FormControlLabel,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Checkbox
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  VideoLibrary,
  People,
  Visibility,
  VisibilityOff
} from '@mui/icons-material';
import axios from 'axios';
import VideoManagement from './VideoManagement';

const CourseManagement = () => {
  const [courses, setCourses] = useState([]);
  const [students, setStudents] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [openStudentsDialog, setOpenStudentsDialog] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [editingCourse, setEditingCourse] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    price: '',
    isActive: true,
    level: 'مبتدئ',
    duration: '',
    category: ''
  });

  useEffect(() => {
    fetchCourses();
    fetchStudents();
  }, []);

  const fetchCourses = async () => {
    try {
      const response = await axios.get('/admin/courses');
      setCourses(response.data);
    } catch (error) {
      console.error('خطأ في جلب الدورات:', error);
      // بيانات تجريبية
      setCourses([
        {
          _id: '1',
          title: 'أساسيات التسويق الرقمي',
          description: 'تعلم أساسيات التسويق الرقمي من الصفر',
          price: 299,
          isActive: true,
          level: 'مبتدئ',
          duration: '8 ساعات',
          category: 'التسويق الرقمي',
          videos: [],
          enrolledStudents: ['2', '3']
        },
        {
          _id: '2',
          title: 'إدارة وسائل التواصل الاجتماعي',
          description: 'كيفية إدارة حسابات التواصل الاجتماعي بفعالية',
          price: 399,
          isActive: true,
          level: 'متوسط',
          duration: '6 ساعات',
          category: 'وسائل التواصل',
          videos: [],
          enrolledStudents: ['2']
        }
      ]);
    }
  };

  const fetchStudents = async () => {
    try {
      const response = await axios.get('/admin/students');
      setStudents(response.data);
    } catch (error) {
      console.error('خطأ في جلب الطلاب:', error);
      setStudents([
        { _id: '2', name: 'أحمد محمد', studentCode: '123456', isActive: true },
        { _id: '3', name: 'فاطمة علي', studentCode: '789012', isActive: true }
      ]);
    }
  };

  const handleOpenDialog = (course = null) => {
    if (course) {
      setEditingCourse(course);
      setFormData({
        title: course.title,
        description: course.description,
        price: course.price,
        isActive: course.isActive,
        level: course.level || 'مبتدئ',
        duration: course.duration || '',
        category: course.category || ''
      });
    } else {
      setEditingCourse(null);
      setFormData({
        title: '',
        description: '',
        price: '',
        isActive: true,
        level: 'مبتدئ',
        duration: '',
        category: ''
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingCourse(null);
    setFormData({
      title: '',
      description: '',
      price: '',
      isActive: true
    });
  };

  const handleSubmit = async () => {
    try {
      if (editingCourse) {
        // تحديث دورة موجودة
        const response = await axios.put(`/admin/courses/${editingCourse._id}`, formData);
        setCourses(courses.map(course => 
          course._id === editingCourse._id ? response.data : course
        ));
      } else {
        // إضافة دورة جديدة
        const response = await axios.post('/admin/courses', formData);
        setCourses([...courses, response.data]);
      }
      handleCloseDialog();
    } catch (error) {
      console.error('خطأ في حفظ الدورة:', error);
      // محاكاة النجاح للاختبار
      const newCourse = {
        _id: Date.now().toString(),
        ...formData,
        videos: [],
        enrolledStudents: 0
      };
      
      if (editingCourse) {
        setCourses(courses.map(course => 
          course._id === editingCourse._id ? { ...editingCourse, ...formData } : course
        ));
      } else {
        setCourses([...courses, newCourse]);
      }
      handleCloseDialog();
    }
  };

  const handleDelete = async (courseId) => {
    if (window.confirm('هل أنت متأكد من حذف هذه الدورة؟')) {
      try {
        await axios.delete(`/admin/courses/${courseId}`);
        setCourses(courses.filter(course => course._id !== courseId));
      } catch (error) {
        console.error('خطأ في حذف الدورة:', error);
        // محاكاة النجاح للاختبار
        setCourses(courses.filter(course => course._id !== courseId));
      }
    }
  };

  const toggleCourseStatus = async (courseId, currentStatus) => {
    try {
      const response = await axios.patch(`/admin/courses/${courseId}/status`, {
        isActive: !currentStatus
      });
      setCourses(courses.map(course => 
        course._id === courseId ? { ...course, isActive: !currentStatus } : course
      ));
    } catch (error) {
      console.error('خطأ في تغيير حالة الدورة:', error);
      // محاكاة النجاح للاختبار
      setCourses(courses.map(course => 
        course._id === courseId ? { ...course, isActive: !currentStatus } : course
      ));
    }
  };

  const handleManageStudents = (course) => {
    setSelectedCourse(course);
    setOpenStudentsDialog(true);
  };

  const handleEnrollStudent = async (studentId) => {
    try {
      await axios.post(`/admin/students/${studentId}/enroll/${selectedCourse._id}`);

      // تحديث البيانات المحلية
      setSelectedCourse(prev => ({
        ...prev,
        enrolledStudents: [...prev.enrolledStudents, studentId]
      }));

      setCourses(courses.map(course =>
        course._id === selectedCourse._id
          ? { ...course, enrolledStudents: [...course.enrolledStudents, studentId] }
          : course
      ));

      alert('تم تسجيل الطالب في الكورس بنجاح');
    } catch (error) {
      console.error('خطأ في تسجيل الطالب:', error);
      alert('حدث خطأ في تسجيل الطالب');
    }
  };

  const handleUnenrollStudent = async (studentId) => {
    try {
      await axios.delete(`/admin/students/${studentId}/unenroll/${selectedCourse._id}`);

      // تحديث البيانات المحلية
      setSelectedCourse(prev => ({
        ...prev,
        enrolledStudents: prev.enrolledStudents.filter(id => id !== studentId)
      }));

      setCourses(courses.map(course =>
        course._id === selectedCourse._id
          ? { ...course, enrolledStudents: course.enrolledStudents.filter(id => id !== studentId) }
          : course
      ));

      alert('تم إلغاء تسجيل الطالب من الكورس بنجاح');
    } catch (error) {
      console.error('خطأ في إلغاء تسجيل الطالب:', error);
      alert('حدث خطأ في إلغاء تسجيل الطالب');
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
          إدارة الدورات
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => handleOpenDialog()}
          sx={{ borderRadius: 2 }}
        >
          إضافة دورة جديدة
        </Button>
      </Box>

      {/* إحصائيات سريعة */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: '#e3f2fd' }}>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#1976d2' }}>
                {courses.length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                إجمالي الدورات
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: '#e8f5e8' }}>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#4caf50' }}>
                {courses.filter(c => c.isActive).length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                الدورات النشطة
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: '#fff3e0' }}>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#ff9800' }}>
                {courses.reduce((total, course) => total + (course.enrolledStudents || 0), 0)}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                إجمالي المسجلين
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: '#f3e5f5' }}>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#9c27b0' }}>
                {courses.reduce((total, course) => total + (course.videos?.length || 0), 0)}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                إجمالي الفيديوهات
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* جدول الدورات */}
      <TableContainer component={Paper} sx={{ borderRadius: 2 }}>
        <Table>
          <TableHead>
            <TableRow sx={{ bgcolor: '#f5f5f5' }}>
              <TableCell sx={{ fontWeight: 'bold' }}>اسم الدورة</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>الوصف</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>السعر</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>المستوى</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>المسجلين</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>الحالة</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>الإجراءات</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {courses.map((course) => (
              <TableRow key={course._id} hover>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <VideoLibrary sx={{ mr: 1, color: '#1976d2' }} />
                    <Typography variant="subtitle2" sx={{ fontWeight: 'medium' }}>
                      {course.title}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" color="textSecondary">
                    {course.description.length > 50 
                      ? `${course.description.substring(0, 50)}...` 
                      : course.description}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="subtitle2" sx={{ fontWeight: 'medium' }}>
                    {course.price} ريال
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip
                    label={course.level || 'مبتدئ'}
                    size="small"
                    color={course.level === 'متقدم' ? 'error' : course.level === 'متوسط' ? 'warning' : 'success'}
                  />
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <People sx={{ mr: 1, color: '#4caf50', fontSize: 20 }} />
                    <Typography variant="body2">
                      {Array.isArray(course.enrolledStudents) ? course.enrolledStudents.length : 0}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={course.isActive}
                        onChange={() => toggleCourseStatus(course._id, course.isActive)}
                        color="primary"
                      />
                    }
                    label={course.isActive ? 'نشط' : 'غير نشط'}
                  />
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <IconButton
                      size="small"
                      onClick={() => handleManageStudents(course)}
                      sx={{ color: '#4caf50' }}
                      title="إدارة الطلاب"
                    >
                      <People />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleOpenDialog(course)}
                      sx={{ color: '#1976d2' }}
                      title="تعديل"
                    >
                      <Edit />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleDelete(course._id)}
                      sx={{ color: '#d32f2f' }}
                      title="حذف"
                    >
                      <Delete />
                    </IconButton>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Dialog لإضافة/تعديل الدورة */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingCourse ? 'تعديل الدورة' : 'إضافة دورة جديدة'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              fullWidth
              label="اسم الدورة"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              sx={{ mb: 3 }}
            />
            
            <TextField
              fullWidth
              label="وصف الدورة"
              multiline
              rows={4}
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              sx={{ mb: 3 }}
            />
            
            <TextField
              fullWidth
              label="السعر (ريال)"
              type="number"
              value={formData.price}
              onChange={(e) => setFormData({ ...formData, price: e.target.value })}
              sx={{ mb: 3 }}
            />

            <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
              <FormControl fullWidth>
                <InputLabel>المستوى</InputLabel>
                <Select
                  value={formData.level}
                  label="المستوى"
                  onChange={(e) => setFormData({ ...formData, level: e.target.value })}
                >
                  <MenuItem value="مبتدئ">مبتدئ</MenuItem>
                  <MenuItem value="متوسط">متوسط</MenuItem>
                  <MenuItem value="متقدم">متقدم</MenuItem>
                </Select>
              </FormControl>

              <TextField
                fullWidth
                label="المدة (مثال: 8 ساعات)"
                value={formData.duration}
                onChange={(e) => setFormData({ ...formData, duration: e.target.value })}
              />
            </Box>

            <TextField
              fullWidth
              label="الفئة"
              value={formData.category}
              onChange={(e) => setFormData({ ...formData, category: e.target.value })}
              sx={{ mb: 3 }}
            />

            <FormControlLabel
              control={
                <Switch
                  checked={formData.isActive}
                  onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                />
              }
              label="دورة نشطة"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>إلغاء</Button>
          <Button onClick={handleSubmit} variant="contained">
            {editingCourse ? 'تحديث' : 'إضافة'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog إدارة الطلاب */}
      <Dialog
        open={openStudentsDialog}
        onClose={() => setOpenStudentsDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          إدارة الطلاب - {selectedCourse?.title}
        </DialogTitle>
        <DialogContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            الطلاب المسجلين ({selectedCourse?.enrolledStudents?.length || 0})
          </Typography>

          <List sx={{ mb: 3 }}>
            {students
              .filter(student => selectedCourse?.enrolledStudents?.includes(student._id))
              .map((student) => (
                <ListItem key={student._id}>
                  <ListItemText
                    primary={student.name}
                    secondary={`كود الطالب: ${student.studentCode}`}
                  />
                  <ListItemSecondaryAction>
                    <Button
                      variant="outlined"
                      color="error"
                      size="small"
                      onClick={() => handleUnenrollStudent(student._id)}
                    >
                      إلغاء التسجيل
                    </Button>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
          </List>

          <Typography variant="h6" sx={{ mb: 2 }}>
            الطلاب المتاحين للتسجيل
          </Typography>

          <List>
            {students
              .filter(student => !selectedCourse?.enrolledStudents?.includes(student._id))
              .map((student) => (
                <ListItem key={student._id}>
                  <ListItemText
                    primary={student.name}
                    secondary={`كود الطالب: ${student.studentCode}`}
                  />
                  <ListItemSecondaryAction>
                    <Button
                      variant="contained"
                      color="primary"
                      size="small"
                      onClick={() => handleEnrollStudent(student._id)}
                    >
                      تسجيل في الكورس
                    </Button>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenStudentsDialog(false)}>إغلاق</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CourseManagement;

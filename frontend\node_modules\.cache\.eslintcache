[{"C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\AdminDashboard.js": "3", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\contexts\\AuthContext.js": "4", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\StudentDashboard.js": "5", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\Login.js": "6", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\LoadingSpinner.js": "7", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\CoursePlayer.js": "8", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\DashboardOverview.js": "9", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\StudentsManagement.js": "10", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CategoriesManagement.js": "11", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CertificatesManagement.js": "12", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CoursesManagement.js": "13", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\StudentManagement.js": "14", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CourseManagement.js": "15", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\CourseViewer.js": "16", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\ContactAdmin.js": "17", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CertificateManagement.js": "18", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\VideoManagement.js": "19"}, {"size": 233, "mtime": 1751936645905, "results": "20", "hashOfConfig": "21"}, {"size": 4915, "mtime": 1751936094183, "results": "22", "hashOfConfig": "21"}, {"size": 9351, "mtime": 1751943110565, "results": "23", "hashOfConfig": "21"}, {"size": 3749, "mtime": 1751936115808, "results": "24", "hashOfConfig": "21"}, {"size": 16665, "mtime": 1751940953156, "results": "25", "hashOfConfig": "21"}, {"size": 9893, "mtime": 1751936169245, "results": "26", "hashOfConfig": "21"}, {"size": 729, "mtime": 1751936128460, "results": "27", "hashOfConfig": "21"}, {"size": 439, "mtime": 1751936452877, "results": "28", "hashOfConfig": "21"}, {"size": 11580, "mtime": 1751942972578, "results": "29", "hashOfConfig": "21"}, {"size": 15293, "mtime": 1751936691832, "results": "30", "hashOfConfig": "21"}, {"size": 0, "mtime": 1751936741843, "results": "31", "hashOfConfig": "21"}, {"size": 457, "mtime": 1751936425851, "results": "32", "hashOfConfig": "21"}, {"size": 449, "mtime": 1751936412497, "results": "33", "hashOfConfig": "21"}, {"size": 14207, "mtime": 1751939616305, "results": "34", "hashOfConfig": "21"}, {"size": 20093, "mtime": 1751943198569, "results": "35", "hashOfConfig": "21"}, {"size": 8715, "mtime": 1751940438358, "results": "36", "hashOfConfig": "21"}, {"size": 7599, "mtime": 1751941008690, "results": "37", "hashOfConfig": "21"}, {"size": 18209, "mtime": 1751943059906, "results": "38", "hashOfConfig": "21"}, {"size": 12008, "mtime": 1751943171875, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "tsclx4", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\AdminDashboard.js", ["97", "98", "99", "100", "101", "102"], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\StudentDashboard.js", ["103", "104"], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\Login.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\LoadingSpinner.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\CoursePlayer.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\DashboardOverview.js", ["105", "106", "107", "108"], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\StudentsManagement.js", ["109", "110", "111", "112", "113"], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CategoriesManagement.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CertificatesManagement.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CoursesManagement.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\StudentManagement.js", ["114", "115", "116"], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CourseManagement.js", ["117", "118", "119", "120", "121"], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\CourseViewer.js", ["122", "123", "124", "125"], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\ContactAdmin.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CertificateManagement.js", ["126", "127"], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\VideoManagement.js", [], [], {"ruleId": "128", "severity": 1, "message": "129", "line": 18, "column": 3, "nodeType": "130", "messageId": "131", "endLine": 18, "endColumn": 8}, {"ruleId": "128", "severity": 1, "message": "132", "line": 19, "column": 3, "nodeType": "130", "messageId": "131", "endLine": 19, "endColumn": 7}, {"ruleId": "128", "severity": 1, "message": "133", "line": 20, "column": 3, "nodeType": "130", "messageId": "131", "endLine": 20, "endColumn": 14}, {"ruleId": "128", "severity": 1, "message": "134", "line": 27, "column": 3, "nodeType": "130", "messageId": "131", "endLine": 27, "endColumn": 9}, {"ruleId": "128", "severity": 1, "message": "135", "line": 35, "column": 3, "nodeType": "130", "messageId": "131", "endLine": 35, "endColumn": 13}, {"ruleId": "128", "severity": 1, "message": "136", "line": 36, "column": 3, "nodeType": "130", "messageId": "131", "endLine": 36, "endColumn": 13}, {"ruleId": "128", "severity": 1, "message": "137", "line": 15, "column": 3, "nodeType": "130", "messageId": "131", "endLine": 15, "endColumn": 7}, {"ruleId": "128", "severity": 1, "message": "138", "line": 32, "column": 3, "nodeType": "130", "messageId": "131", "endLine": 32, "endColumn": 13}, {"ruleId": "128", "severity": 1, "message": "135", "line": 24, "column": 3, "nodeType": "130", "messageId": "131", "endLine": 24, "endColumn": 13}, {"ruleId": "128", "severity": 1, "message": "139", "line": 26, "column": 3, "nodeType": "130", "messageId": "131", "endLine": 26, "endColumn": 14}, {"ruleId": "128", "severity": 1, "message": "138", "line": 27, "column": 3, "nodeType": "130", "messageId": "131", "endLine": 27, "endColumn": 13}, {"ruleId": "128", "severity": 1, "message": "140", "line": 92, "column": 10, "nodeType": "130", "messageId": "131", "endLine": 92, "endColumn": 17}, {"ruleId": "128", "severity": 1, "message": "141", "line": 35, "column": 3, "nodeType": "130", "messageId": "131", "endLine": 35, "endColumn": 13}, {"ruleId": "128", "severity": 1, "message": "134", "line": 36, "column": 3, "nodeType": "130", "messageId": "131", "endLine": 36, "endColumn": 9}, {"ruleId": "128", "severity": 1, "message": "140", "line": 49, "column": 10, "nodeType": "130", "messageId": "131", "endLine": 49, "endColumn": 17}, {"ruleId": "142", "severity": 1, "message": "143", "line": 67, "column": 6, "nodeType": "144", "endLine": 67, "endColumn": 56, "suggestions": "145"}, {"ruleId": "128", "severity": 1, "message": "146", "line": 180, "column": 9, "nodeType": "130", "messageId": "131", "endLine": 180, "endColumn": 16}, {"ruleId": "128", "severity": 1, "message": "134", "line": 32, "column": 3, "nodeType": "130", "messageId": "131", "endLine": 32, "endColumn": 9}, {"ruleId": "128", "severity": 1, "message": "141", "line": 34, "column": 3, "nodeType": "130", "messageId": "131", "endLine": 34, "endColumn": 13}, {"ruleId": "128", "severity": 1, "message": "147", "line": 166, "column": 13, "nodeType": "130", "messageId": "131", "endLine": 166, "endColumn": 21}, {"ruleId": "128", "severity": 1, "message": "148", "line": 33, "column": 3, "nodeType": "130", "messageId": "131", "endLine": 33, "endColumn": 11}, {"ruleId": "128", "severity": 1, "message": "141", "line": 41, "column": 3, "nodeType": "130", "messageId": "131", "endLine": 41, "endColumn": 13}, {"ruleId": "128", "severity": 1, "message": "149", "line": 42, "column": 3, "nodeType": "130", "messageId": "131", "endLine": 42, "endColumn": 16}, {"ruleId": "128", "severity": 1, "message": "150", "line": 45, "column": 8, "nodeType": "130", "messageId": "131", "endLine": 45, "endColumn": 23}, {"ruleId": "128", "severity": 1, "message": "147", "line": 206, "column": 13, "nodeType": "130", "messageId": "131", "endLine": 206, "endColumn": 21}, {"ruleId": "128", "severity": 1, "message": "151", "line": 23, "column": 3, "nodeType": "130", "messageId": "131", "endLine": 23, "endColumn": 7}, {"ruleId": "128", "severity": 1, "message": "152", "line": 30, "column": 8, "nodeType": "130", "messageId": "131", "endLine": 30, "endColumn": 13}, {"ruleId": "128", "severity": 1, "message": "153", "line": 33, "column": 11, "nodeType": "130", "messageId": "131", "endLine": 33, "endColumn": 15}, {"ruleId": "142", "severity": 1, "message": "154", "line": 42, "column": 6, "nodeType": "144", "endLine": 42, "endColumn": 14, "suggestions": "155"}, {"ruleId": "128", "severity": 1, "message": "134", "line": 39, "column": 3, "nodeType": "130", "messageId": "131", "endLine": 39, "endColumn": 9}, {"ruleId": "128", "severity": 1, "message": "147", "line": 172, "column": 15, "nodeType": "130", "messageId": "131", "endLine": 172, "endColumn": 23}, "no-unused-vars", "'Badge' is defined but never used.", "Identifier", "unusedVar", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'School' is defined but never used.", "'TrendingUp' is defined but never used.", "'Assignment' is defined but never used.", "'Chip' is defined but never used.", "'AccessTime' is defined but never used.", "'CheckCircle' is defined but never used.", "'loading' is assigned a value but never used.", "'Visibility' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchStudents'. Either include it or remove the dependency array.", "ArrayExpression", ["156"], "'columns' is assigned a value but never used.", "'response' is assigned a value but never used.", "'Checkbox' is defined but never used.", "'VisibilityOff' is defined but never used.", "'VideoManagement' is defined but never used.", "'Lock' is defined but never used.", "'axios' is defined but never used.", "'user' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchCourseProgress'. Either include it or remove the dependency array.", ["157"], {"desc": "158", "fix": "159"}, {"desc": "160", "fix": "161"}, "Update the dependencies array to be: [fetchStudents, pagination.page, pagination.pageSize, searchTerm]", {"range": "162", "text": "163"}, "Update the dependencies array to be: [course, fetchCourseProgress]", {"range": "164", "text": "165"}, [1278, 1328], "[fetchStudents, pagination.page, pagination.pageSize, searchTerm]", [836, 844], "[course, fetchCourseProgress]"]
[{"C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\AdminDashboard.js": "3", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\contexts\\AuthContext.js": "4", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\StudentDashboard.js": "5", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\Login.js": "6", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\LoadingSpinner.js": "7", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\CoursePlayer.js": "8", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\DashboardOverview.js": "9", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\StudentsManagement.js": "10", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CategoriesManagement.js": "11", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CertificatesManagement.js": "12", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CoursesManagement.js": "13", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\StudentManagement.js": "14", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CourseManagement.js": "15", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\CourseViewer.js": "16", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\ContactAdmin.js": "17"}, {"size": 233, "mtime": 1751936645905, "results": "18", "hashOfConfig": "19"}, {"size": 4915, "mtime": 1751936094183, "results": "20", "hashOfConfig": "19"}, {"size": 9594, "mtime": 1751941382626, "results": "21", "hashOfConfig": "19"}, {"size": 3749, "mtime": 1751936115808, "results": "22", "hashOfConfig": "19"}, {"size": 16665, "mtime": 1751940953156, "results": "23", "hashOfConfig": "19"}, {"size": 9893, "mtime": 1751936169245, "results": "24", "hashOfConfig": "19"}, {"size": 729, "mtime": 1751936128460, "results": "25", "hashOfConfig": "19"}, {"size": 439, "mtime": 1751936452877, "results": "26", "hashOfConfig": "19"}, {"size": 11639, "mtime": 1751941839359, "results": "27", "hashOfConfig": "19"}, {"size": 15293, "mtime": 1751936691832, "results": "28", "hashOfConfig": "19"}, {"size": 0, "mtime": 1751936741843, "results": "29", "hashOfConfig": "19"}, {"size": 457, "mtime": 1751936425851, "results": "30", "hashOfConfig": "19"}, {"size": 449, "mtime": 1751936412497, "results": "31", "hashOfConfig": "19"}, {"size": 14207, "mtime": 1751939616305, "results": "32", "hashOfConfig": "19"}, {"size": 20044, "mtime": 1751940299834, "results": "33", "hashOfConfig": "19"}, {"size": 8715, "mtime": 1751940438358, "results": "34", "hashOfConfig": "19"}, {"size": 7599, "mtime": 1751941008690, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "tsclx4", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\AdminDashboard.js", ["87", "88", "89", "90", "91", "92"], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\StudentDashboard.js", ["93", "94"], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\Login.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\LoadingSpinner.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\CoursePlayer.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\DashboardOverview.js", ["95", "96", "97", "98"], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\StudentsManagement.js", ["99", "100", "101", "102", "103"], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CategoriesManagement.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CertificatesManagement.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CoursesManagement.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\StudentManagement.js", ["104", "105", "106"], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CourseManagement.js", ["107", "108", "109", "110"], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\CourseViewer.js", ["111", "112", "113", "114"], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\ContactAdmin.js", [], [], {"ruleId": "115", "severity": 1, "message": "116", "line": 18, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 18, "endColumn": 8}, {"ruleId": "115", "severity": 1, "message": "119", "line": 19, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 19, "endColumn": 7}, {"ruleId": "115", "severity": 1, "message": "120", "line": 20, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 20, "endColumn": 14}, {"ruleId": "115", "severity": 1, "message": "121", "line": 27, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 27, "endColumn": 9}, {"ruleId": "115", "severity": 1, "message": "122", "line": 35, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 35, "endColumn": 13}, {"ruleId": "115", "severity": 1, "message": "123", "line": 36, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 36, "endColumn": 13}, {"ruleId": "115", "severity": 1, "message": "124", "line": 15, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 15, "endColumn": 7}, {"ruleId": "115", "severity": 1, "message": "125", "line": 32, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 32, "endColumn": 13}, {"ruleId": "115", "severity": 1, "message": "122", "line": 23, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 23, "endColumn": 13}, {"ruleId": "115", "severity": 1, "message": "126", "line": 25, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 25, "endColumn": 14}, {"ruleId": "115", "severity": 1, "message": "125", "line": 26, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 26, "endColumn": 13}, {"ruleId": "115", "severity": 1, "message": "127", "line": 90, "column": 10, "nodeType": "117", "messageId": "118", "endLine": 90, "endColumn": 17}, {"ruleId": "115", "severity": 1, "message": "128", "line": 35, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 35, "endColumn": 13}, {"ruleId": "115", "severity": 1, "message": "121", "line": 36, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 36, "endColumn": 9}, {"ruleId": "115", "severity": 1, "message": "127", "line": 49, "column": 10, "nodeType": "117", "messageId": "118", "endLine": 49, "endColumn": 17}, {"ruleId": "129", "severity": 1, "message": "130", "line": 67, "column": 6, "nodeType": "131", "endLine": 67, "endColumn": 56, "suggestions": "132"}, {"ruleId": "115", "severity": 1, "message": "133", "line": 180, "column": 9, "nodeType": "117", "messageId": "118", "endLine": 180, "endColumn": 16}, {"ruleId": "115", "severity": 1, "message": "121", "line": 32, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 32, "endColumn": 9}, {"ruleId": "115", "severity": 1, "message": "128", "line": 34, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 34, "endColumn": 13}, {"ruleId": "115", "severity": 1, "message": "134", "line": 166, "column": 13, "nodeType": "117", "messageId": "118", "endLine": 166, "endColumn": 21}, {"ruleId": "115", "severity": 1, "message": "135", "line": 33, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 33, "endColumn": 11}, {"ruleId": "115", "severity": 1, "message": "128", "line": 41, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 41, "endColumn": 13}, {"ruleId": "115", "severity": 1, "message": "136", "line": 42, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 42, "endColumn": 16}, {"ruleId": "115", "severity": 1, "message": "134", "line": 205, "column": 13, "nodeType": "117", "messageId": "118", "endLine": 205, "endColumn": 21}, {"ruleId": "115", "severity": 1, "message": "137", "line": 23, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 23, "endColumn": 7}, {"ruleId": "115", "severity": 1, "message": "138", "line": 30, "column": 8, "nodeType": "117", "messageId": "118", "endLine": 30, "endColumn": 13}, {"ruleId": "115", "severity": 1, "message": "139", "line": 33, "column": 11, "nodeType": "117", "messageId": "118", "endLine": 33, "endColumn": 15}, {"ruleId": "129", "severity": 1, "message": "140", "line": 42, "column": 6, "nodeType": "131", "endLine": 42, "endColumn": 14, "suggestions": "141"}, "no-unused-vars", "'Badge' is defined but never used.", "Identifier", "unusedVar", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'School' is defined but never used.", "'TrendingUp' is defined but never used.", "'Assignment' is defined but never used.", "'Chip' is defined but never used.", "'AccessTime' is defined but never used.", "'CheckCircle' is defined but never used.", "'loading' is assigned a value but never used.", "'Visibility' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchStudents'. Either include it or remove the dependency array.", "ArrayExpression", ["142"], "'columns' is assigned a value but never used.", "'response' is assigned a value but never used.", "'Checkbox' is defined but never used.", "'VisibilityOff' is defined but never used.", "'Lock' is defined but never used.", "'axios' is defined but never used.", "'user' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchCourseProgress'. Either include it or remove the dependency array.", ["143"], {"desc": "144", "fix": "145"}, {"desc": "146", "fix": "147"}, "Update the dependencies array to be: [fetchStudents, pagination.page, pagination.pageSize, searchTerm]", {"range": "148", "text": "149"}, "Update the dependencies array to be: [course, fetchCourseProgress]", {"range": "150", "text": "151"}, [1278, 1328], "[fetchStudents, pagination.page, pagination.pageSize, searchTerm]", [836, 844], "[course, fetchCourseProgress]"]